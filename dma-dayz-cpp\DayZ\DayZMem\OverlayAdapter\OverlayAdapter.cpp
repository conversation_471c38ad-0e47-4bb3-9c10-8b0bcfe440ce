#include "OverlayAdapter.h"
#include <imgui.h>
#include <unordered_set>
#include <chrono>
#include <cmath>
#include <set>
#include <fstream>
#include <iostream>
#include "../../../DMALib/includes/DMARender/LootList/LootList.h"
#include "../../Structs/SkeletonESP.h"
#include "../../Structs/SkeletonESP.h"

// Constructor
DayZ::OverlayAdapter::OverlayAdapter(std::shared_ptr<DayZ::MemoryUpdater> memUpdater, std::shared_ptr<DMARender::RenderBridge> renderBridge)
	: memUpdater(memUpdater), renderBridge(renderBridge) {

	// Set the bridge in the base IOverlay class for font style access
	setBridge(renderBridge);

	// Initialize multithreaded skeleton processing system
	SkeletonESP::InitializeWorkerSystem(4); // Use 4 worker threads for optimal performance

	//loadFavoriteSteamIDs("steamids.txt"); // Read "steamids.txt" for your favorite admins
	// Note: LootListManager handles rare items loading, Entity system syncs from it
}

DayZ::OverlayAdapter::~OverlayAdapter() {
	// Cleanup multithreaded skeleton processing system
	SkeletonESP::ShutdownWorkerSystem();
}

// DayZ2-style text overlap prevention implementation
DMARender::Vector2 DayZ::OverlayAdapter::getAvailableTextPosition(const DMARender::Vector2& basePosition, float lineHeight) {
	DMARender::Vector2 textPosition = basePosition;

	// Round to nearest pixel to avoid floating point precision issues
	float roundedX = std::round(textPosition.x);
	float roundedY = std::round(textPosition.y);

	// Improved overlap prevention: Check multiple positions and find the best one
	const int maxAttempts = 10; // Maximum attempts to find a position
	const float horizontalStep = 5.0f; // Horizontal step for alternative positions
	const float verticalStep = lineHeight; // Vertical step (default 20px)
	
	// Try multiple positions in a smart pattern
	for (int attempt = 0; attempt < maxAttempts; attempt++) {
		float testX = roundedX;
		float testY = roundedY;
		
		// Pattern: Try different positions in order of preference
		switch (attempt) {
			case 0: // Original position
				break;
			case 1: // Slightly to the right
				testX += horizontalStep;
				break;
			case 2: // Slightly to the left
				testX -= horizontalStep;
				break;
			case 3: // Down and right
				testY += verticalStep;
				testX += horizontalStep;
				break;
			case 4: // Down and left
				testY += verticalStep;
				testX -= horizontalStep;
				break;
			case 5: // Just down
				testY += verticalStep;
				break;
			case 6: // Further down
				testY += verticalStep * 2;
				break;
			case 7: // Up and right
				testY -= verticalStep;
				testX += horizontalStep;
				break;
			case 8: // Up and left
				testY -= verticalStep;
				testX -= horizontalStep;
				break;
			case 9: // Just up
				testY -= verticalStep;
				break;
		}
		
		// Check if this position is available
		bool positionAvailable = true;
		
		// Check a small area around the position (not just exact match)
		for (float checkX = testX - 2.0f; checkX <= testX + 2.0f; checkX += 1.0f) {
			for (float checkY = testY - 2.0f; checkY <= testY + 2.0f; checkY += 1.0f) {
				float roundedCheckX = std::round(checkX);
				float roundedCheckY = std::round(checkY);
				
				if (textPositions[std::make_pair(roundedCheckX, roundedCheckY)]) {
					positionAvailable = false;
					break;
				}
			}
			if (!positionAvailable) break;
		}
		
		if (positionAvailable) {
			// Mark this position and surrounding area as occupied
			for (float markX = testX - 2.0f; markX <= testX + 2.0f; markX += 1.0f) {
				for (float markY = testY - 2.0f; markY <= testY + 2.0f; markY += 1.0f) {
					float roundedMarkX = std::round(markX);
					float roundedMarkY = std::round(markY);
					textPositions[std::make_pair(roundedMarkX, roundedMarkY)] = true;
				}
			}
			
			return DMARender::Vector2(testX, testY);
		}
	}
	
	// If no position found, use the original position and mark it
	textPositions[std::make_pair(roundedX, roundedY)] = true;
	return DMARender::Vector2(roundedX, roundedY);
}

void DayZ::OverlayAdapter::clearTextPositionsInArea(const DMARender::Vector2& center, float radius) {
	// Clear text positions in a circular area around the center point
	// This is useful for clearing old text positions when entities move
	
	float radiusSquared = radius * radius;
	
	// Remove positions within the radius
	for (auto it = textPositions.begin(); it != textPositions.end();) {
		float dx = it->first.first - center.x;
		float dy = it->first.second - center.y;
		float distanceSquared = dx * dx + dy * dy;
		
		if (distanceSquared <= radiusSquared) {
			it = textPositions.erase(it);
		} else {
			++it;
		}
	}
}

// DayZ2-style enhanced font system helpers (simplified implementation)
void DayZ::OverlayAdapter::drawEnhancedText(const std::string& text, const DMARender::Vector2& position,
                                           FontType fontType, ImU32 color,
                                           bool useStroke) {
	ImDrawList* drawList = ImGui::GetForegroundDrawList();
	ImVec2 pos(position.x, position.y);

	// DayZ2-style text stroke (4-direction black outline)
	if (useStroke) {
		ImU32 strokeColor = IM_COL32(0, 0, 0, 255); // Black outline
		float strokeWidth = 1.0f;

		// Draw stroke in 4 diagonal directions (DayZ2 style)
		drawList->AddText(ImVec2(pos.x + strokeWidth, pos.y + strokeWidth), strokeColor, text.c_str());
		drawList->AddText(ImVec2(pos.x - strokeWidth, pos.y - strokeWidth), strokeColor, text.c_str());
		drawList->AddText(ImVec2(pos.x + strokeWidth, pos.y - strokeWidth), strokeColor, text.c_str());
		drawList->AddText(ImVec2(pos.x - strokeWidth, pos.y + strokeWidth), strokeColor, text.c_str());
	}

	// Draw main text
	drawList->AddText(pos, color, text.c_str());
}

void DayZ::OverlayAdapter::drawEnhancedTextWithSize(const std::string& text, const DMARender::Vector2& position,
                                                   float fontSize, ImU32 color, bool useStroke) {
	ImDrawList* drawList = ImGui::GetForegroundDrawList();
	ImVec2 pos(position.x, position.y);
	ImFont* font = ImGui::GetFont(); // Use current font

	// DayZ2-style text stroke (4-direction black outline)
	if (useStroke) {
		ImU32 strokeColor = IM_COL32(0, 0, 0, 255); // Black outline
		float strokeWidth = 1.0f;

		// Draw stroke in 4 diagonal directions (DayZ2 style)
		drawList->AddText(font, fontSize, ImVec2(pos.x + strokeWidth, pos.y + strokeWidth), strokeColor, text.c_str());
		drawList->AddText(font, fontSize, ImVec2(pos.x - strokeWidth, pos.y - strokeWidth), strokeColor, text.c_str());
		drawList->AddText(font, fontSize, ImVec2(pos.x + strokeWidth, pos.y - strokeWidth), strokeColor, text.c_str());
		drawList->AddText(font, fontSize, ImVec2(pos.x - strokeWidth, pos.y + strokeWidth), strokeColor, text.c_str());
	}

	// Draw main text
	drawList->AddText(font, fontSize, pos, color, text.c_str());
}

// CS2-style centered text drawing function
void DayZ::OverlayAdapter::drawCenteredTextWithSize(const std::string& text, const DMARender::Vector2& position,
                                                   float fontSize, ImU32 color, bool useStroke) {
	ImDrawList* drawList = ImGui::GetForegroundDrawList();
	ImFont* font = ImGui::GetFont(); // Use current font

	// Calculate text size for centering
	ImVec2 textSize = font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, text.c_str());
	
	// Center the text horizontally around the given position
	ImVec2 centeredPos(position.x - textSize.x * 0.5f, position.y);

	// DayZ2-style text stroke (4-direction black outline)
	if (useStroke) {
		ImU32 strokeColor = IM_COL32(0, 0, 0, 255); // Black outline
		float strokeWidth = 1.0f;

		// Draw stroke in 4 diagonal directions (DayZ2 style)
		drawList->AddText(font, fontSize, ImVec2(centeredPos.x + strokeWidth, centeredPos.y + strokeWidth), strokeColor, text.c_str());
		drawList->AddText(font, fontSize, ImVec2(centeredPos.x - strokeWidth, centeredPos.y - strokeWidth), strokeColor, text.c_str());
		drawList->AddText(font, fontSize, ImVec2(centeredPos.x + strokeWidth, centeredPos.y - strokeWidth), strokeColor, text.c_str());
		drawList->AddText(font, fontSize, ImVec2(centeredPos.x - strokeWidth, centeredPos.y + strokeWidth), strokeColor, text.c_str());
	}

	// Draw main text
	drawList->AddText(font, fontSize, centeredPos, color, text.c_str());
}

void DayZ::OverlayAdapter::drawLoot(DayZ::Camera* camera, const std::vector<std::shared_ptr<DayZ::Entity>>& entities) {
	if (!renderBridge) return; // If the render bridge is not initialized, return

	bool showVehicles = renderBridge->shouldShowVehiclesFUSER();
	bool showBoats = renderBridge->shouldShowBoatsFUSER();
	bool showGrounditems = renderBridge->shouldShowGrounditemsFUSER();
	bool showDeadAnimals = renderBridge->shouldShowDeadAnimalsFUSER();
	bool showDeadPlayers = renderBridge->shouldShowDeadPlayersFUSER();
	bool showWeapons = renderBridge->shouldShowWeaponsFUSER();
	bool showClothing = renderBridge->shouldShowClothingFUSER();
	bool showBackpacks = renderBridge->shouldShowBackpacksFUSER();
	bool showProxyMagazines = renderBridge->shouldShowProxyMagazinesFUSER();
	bool showFood = renderBridge->shouldShowFoodFUSER();
	bool showAmmo = renderBridge->shouldShowAmmoFUSER();
	bool showRare = renderBridge->shouldShowRareFUSER();
	int showLootDistance = renderBridge->shouldlootDistanceFUSER();
			bool showOptics = renderBridge->shouldShowOpticsFUSER();
		bool showBase = renderBridge->shouldShowBaseFUSER();
		bool showMelee = renderBridge->shouldShowMeleeFUSER();
		bool showExplosives = renderBridge->shouldShowExplosivesFUSER();
		bool showContainer = renderBridge->shouldShowContainerFUSER();
		bool showCooking = renderBridge->shouldShowCookingFUSER();
		bool showCamping = renderBridge->shouldShowCampingFUSER();
		bool showStash = renderBridge->shouldShowStashFUSER();

	// Early exit if no loot features are enabled
	if (!showVehicles && !showBoats && !showGrounditems && !showDeadAnimals && !showDeadPlayers &&
		!showWeapons && !showClothing && !showBackpacks && !showProxyMagazines && !showFood &&
		!showAmmo && !showRare && !showOptics && !showBase && !showMelee && !showExplosives &&
		!showContainer && !showCooking && !showCamping && !showStash) {
		return;
	}

	// Phase 3: Entity Consolidation - TEMPORARILY DISABLED due to filtering issues
	// std::vector<ConsolidatedEntity> consolidatedEntities = consolidateEntityTables();
	// std::vector<std::shared_ptr<DayZ::Entity>> uniqueLootEntities = extractUniqueEntities(consolidatedEntities, true, false);

	// Filter entities based on enabled features
	std::vector<std::shared_ptr<DayZ::Entity>> filteredEntities;
	filteredEntities.reserve(entities.size());

	for (const auto& item : entities) {
		// Early exit for performance - skip null or incomplete entities immediately
		if (!item || !item->EntityTypePtr || !item->FutureVisualStatePtr) {
			continue;
		}



		// Quick blacklist check before expensive isValid() call
		if (item->EntityTypePtr->TypeName && item->EntityTypePtr->TypeName->value) {
			std::string typeName = item->EntityTypePtr->TypeName->value;
			if (typeName.find("Clutter") != std::string::npos ||
				typeName.find("Debug") != std::string::npos ||
				typeName.find("$UNT$") != std::string::npos) {
				continue; // Skip known problematic entities
			}
		}

		// ESP Debug Logging - Log all entities if enabled
		if (renderBridge && renderBridge->getEnableESPDebugLogging()) {
			logAllEntities(item, "LootEntity");
		}

		if (!item->isValid()) {
			// ESP Debug Logging - Log suspicious entities if enabled
			if (renderBridge && renderBridge->getEnableESPDebugLogging()) {
				logSuspiciousEntity(item, "Invalid_Entity", true);
			}
			continue;
		}

		// Distance check
		float dist = camera->InvertedViewTranslation.Dist(item->FutureVisualStatePtr->position);
		if (dist > 5000) continue; // Max distance check

		// Entity-specific distance checks
		if (item->isCar() && dist > renderBridge->getVehicleMaxDistance()) continue;
		if (item->isBoat() && dist > renderBridge->getBoatMaxDistance()) continue;
		if (item->isAnimal() && !item->isDead && dist > renderBridge->getAnimalMaxDistance()) continue;
		if (item->isPlayer() && item->isDead && dist > renderBridge->getDeadPlayerMaxDistance()) continue;
		if (item->isAnimal() && item->isDead && dist > renderBridge->getDeadAnimalMaxDistance()) continue;
		if (!item->isCar() && !item->isBoat() && !item->isAnimal() && !(item->isPlayer() && item->isDead) && !(item->isAnimal() && item->isDead) && dist > showLootDistance) continue;



		// Filter by enabled entity types
		bool shouldShow = false;
		if (item->isPlayer() && item->isDead && showDeadPlayers) shouldShow = true;
		else if (item->isAnimal() && item->isDead && showDeadAnimals) shouldShow = true;
		else if (item->isCar() && showVehicles) shouldShow = true;
		else if (item->isBoat() && showBoats) shouldShow = true;
		else if (item->isRare() && showRare) shouldShow = true;
		else if (item->isBackpack() && showBackpacks) shouldShow = true;
		else if (item->isClothing() && showClothing) shouldShow = true;
		else if (item->isWeapon() && showWeapons) shouldShow = true;
		else if (item->isProxyMagazines() && showProxyMagazines) shouldShow = true;
		else if (item->isFood() && showFood) shouldShow = true;
		else if (item->isAmmo() && showAmmo) shouldShow = true;
		else if (item->isGroundItem() && showGrounditems) shouldShow = true;
		else if (item->isOptic() && showOptics) shouldShow = true;
		else if (item->isBase() && showBase) shouldShow = true;
		else if (item->isMelee() && showMelee) shouldShow = true;
		else if (item->isExplosives() && showExplosives) shouldShow = true;
		else if (item->isContainer() && showContainer) shouldShow = true;
		else if (item->isCooking() && showCooking) shouldShow = true;
		else if (item->isCamping() && showCamping) shouldShow = true;
		else if (item->isStash() && showStash) shouldShow = true;

		if (shouldShow) {
			filteredEntities.push_back(item);
		}
	}

	// Phase 5: Use type-based rendering for efficient batch processing
	std::vector<TypedEntity> typedEntities = categorizeEntities(filteredEntities, camera, true);
	renderEntitiesByType(typedEntities);
}

bool DayZ::OverlayAdapter::WorldToScreenDayZ(DayZ::Camera* camera, const DMARender::Vector3& position, DMARender::Vector2& outVector)
{
	if (!camera) return false;

	// IMPROVED PRECISION: Use double precision for better accuracy at distance
	DMARender::Vector3 temp = DMARender::Vector3(
		position.x - camera->InvertedViewTranslation.x,
		position.y - camera->InvertedViewTranslation.y,
		position.z - camera->InvertedViewTranslation.z
	);

	double x = temp.Dot(camera->InvertedViewRight);
	double y = temp.Dot(camera->InvertedViewUp);
	double z = temp.Dot(camera->InvertedViewForward);

	// IMPROVED DEPTH CHECK: Better near plane handling
	if (z < 0.1) return false;

	ImVec2 windowSize = ImGui::GetWindowSize();

	// IMPROVED PROJECTION: Higher precision calculations for better accuracy
	double normalizedX = (x / camera->GetProjectionD1.x) / z;
	double normalizedY = (y / camera->GetProjectionD2.y) / z;

	// IMPROVED SCREEN MAPPING: More precise screen coordinate calculation
	outVector.x = (float)((windowSize.x * 0.5) + (normalizedX * (windowSize.x * 0.5)));
	outVector.y = (float)((windowSize.y * 0.5) - (normalizedY * (windowSize.y * 0.5)));

	// BOUNDS CHECK: Ensure coordinates are within reasonable screen bounds
	if (outVector.x < -100 || outVector.x > windowSize.x + 100 ||
		outVector.y < -100 || outVector.y > windowSize.y + 100) {
		return false;
	}

	return true;
}

// WorldToScreen batching optimization - Phase 1
// Processes multiple world positions at once for better performance
std::vector<DayZ::OverlayAdapter::BatchedScreenPosition> DayZ::OverlayAdapter::WorldToScreenBatch(DayZ::Camera* camera, const std::vector<DMARender::Vector3>& positions)
{
	std::vector<BatchedScreenPosition> results;
	results.reserve(positions.size());
	
	if (!camera) {
		// Return empty results if no camera
		for (size_t i = 0; i < positions.size(); ++i) {
			results.push_back({DMARender::Vector2(0, 0), false, 0.0f, i});
		}
		return results;
	}
	
	// Pre-calculate common values once for all positions
	ImVec2 windowSize = ImGui::GetWindowSize();
	float halfWidth = windowSize.x * 0.5f;
	float halfHeight = windowSize.y * 0.5f;
	
	// Process all positions in batch
	for (size_t i = 0; i < positions.size(); ++i) {
		const auto& position = positions[i];
		BatchedScreenPosition result;
		result.originalIndex = i;
		
		// Calculate distance first for early filtering
		result.distance = camera->InvertedViewTranslation.Dist(position);
		
		// Transform to camera space
		DMARender::Vector3 temp = DMARender::Vector3(
			position.x - camera->InvertedViewTranslation.x,
			position.y - camera->InvertedViewTranslation.y,
			position.z - camera->InvertedViewTranslation.z
		);
		
		double x = temp.Dot(camera->InvertedViewRight);
		double y = temp.Dot(camera->InvertedViewUp);
		double z = temp.Dot(camera->InvertedViewForward);
		
		// Check if behind camera
		if (z < 0.1) {
			result.isVisible = false;
			result.screenPos = DMARender::Vector2(0, 0);
			results.push_back(result);
			continue;
		}

		// Project to screen space
		double normalizedX = (x / camera->GetProjectionD1.x) / z;
		double normalizedY = (y / camera->GetProjectionD2.y) / z;
		
		// Convert to screen coordinates
		result.screenPos.x = (float)(halfWidth + (normalizedX * halfWidth));
		result.screenPos.y = (float)(halfHeight - (normalizedY * halfHeight));
		
		// Simple angular culling for performance (Phase 4 optimization)
		// Only cull entities that are way outside reasonable viewing angle
		bool isWithinReasonableAngle = true;
		if (result.distance > 100.0f) { // Only apply angular culling beyond 100m
			DMARender::Vector3 toEntity = temp; // Already calculated above
			float forwardDot = toEntity.Dot(camera->InvertedViewForward);
			float entityDistanceSquared = toEntity.x * toEntity.x + toEntity.y * toEntity.y + toEntity.z * toEntity.z;
			
			// Simple angle check: if dot product is negative and entity is far, it's behind
			if (forwardDot < 0 && entityDistanceSquared > 0) {
				isWithinReasonableAngle = false;
			}
		}
		
		// Check if within reasonable screen bounds
		bool isOnScreen = (result.screenPos.x >= -100 && result.screenPos.x <= windowSize.x + 100 &&
						   result.screenPos.y >= -100 && result.screenPos.y <= windowSize.y + 100);
		
		result.isVisible = isWithinReasonableAngle && isOnScreen;
		
		results.push_back(result);
	}
	
	return results;
}

// Phase 2: Distance-based batching optimization
// Creates a distance-sorted batch with screen positions for efficient LOD rendering
std::vector<DayZ::OverlayAdapter::EntityDistanceInfo> DayZ::OverlayAdapter::createDistanceSortedBatch(
	const std::vector<std::shared_ptr<DayZ::Entity>>& entities, 
	const std::vector<BatchedScreenPosition>& batchedResults)
{
	std::vector<EntityDistanceInfo> distanceInfo;
	distanceInfo.reserve(entities.size());
	
	for (size_t i = 0; i < entities.size(); ++i) {
		const auto& entity = entities[i];
		
		// Skip invalid entities
		if (!entity || !entity->isValid() || entity->isDead) {
			continue;
		}
		
		// Get screen positions from batch results (each entity has 2 positions)
		size_t originIndex = i * 2;
		size_t topIndex = i * 2 + 1;
		
		if (originIndex >= batchedResults.size() || topIndex >= batchedResults.size()) {
			continue; // Safety check
		}
		
		const auto& originResult = batchedResults[originIndex];
		const auto& topResult = batchedResults[topIndex];
		
		// Skip if not visible
		if (!originResult.isVisible || !topResult.isVisible) {
			continue;
		}
		
		EntityDistanceInfo info;
		info.entity = entity;
		info.distance = originResult.distance;
		info.originalIndex = i;
		info.originScreen = originResult;
		info.topScreen = topResult;
		
		distanceInfo.push_back(info);
	}
	
	// Sort by distance (closest first for priority rendering)
	std::sort(distanceInfo.begin(), distanceInfo.end(), 
		[](const EntityDistanceInfo& a, const EntityDistanceInfo& b) {
			return a.distance < b.distance;
		});
	
	return distanceInfo;
}

// Determines rendering quality based on distance and entity type
DayZ::OverlayAdapter::RenderQuality DayZ::OverlayAdapter::determineRenderQuality(float distance, bool isPlayer, bool isZombie, bool isAnimal)
{
	// Players get priority rendering at much longer distances - they're the most important!
	if (isPlayer) {
		if (distance <= 1000.0f) return RenderQuality::HIGH_DETAIL;    // TEMP: Increased from 200m to 1000m for testing
		if (distance <= 2000.0f) return RenderQuality::MEDIUM_DETAIL;  // TEMP: Increased from 600m to 2000m for testing
		if (distance <= 3000.0f) return RenderQuality::LOW_DETAIL;     // TEMP: Increased from 1500m to 3000m for testing
		return RenderQuality::SKIP_RENDER;  // Only skip beyond 3000m
	}
	
	// Zombies get medium priority - FIXED: Increased distance ranges to match user settings
	if (isZombie) {
		if (distance <= 300.0f) return RenderQuality::HIGH_DETAIL;     // Increased from 200m to 300m
		if (distance <= 600.0f) return RenderQuality::MEDIUM_DETAIL;   // Increased from 400m to 600m
		if (distance <= 1000.0f) return RenderQuality::LOW_DETAIL;     // Increased from 600m to 1000m
		return RenderQuality::SKIP_RENDER;  // Only skip beyond 1000m
	}
	
	// Animals get lower priority - FIXED: Increased distance ranges to match user settings
	if (isAnimal) {
		if (distance <= 200.0f) return RenderQuality::HIGH_DETAIL;     // Increased from 80m to 200m
		if (distance <= 500.0f) return RenderQuality::MEDIUM_DETAIL;   // Increased from 200m to 500m
		if (distance <= 1000.0f) return RenderQuality::LOW_DETAIL;     // Increased from 300m to 1000m
		return RenderQuality::SKIP_RENDER;  // Only skip beyond 1000m
	}
	
	return RenderQuality::SKIP_RENDER;
}

// Phase 3: Entity consolidation optimization
// Consolidates entities from all tables into a single deduplicated collection
std::vector<DayZ::OverlayAdapter::ConsolidatedEntity> DayZ::OverlayAdapter::consolidateEntityTables()
{
	std::vector<ConsolidatedEntity> consolidated;
	std::unordered_set<uint32_t> processedIds; // Track processed entity IDs to avoid duplicates
	
	// Helper lambda to add entities from a table with proper type detection
	auto addEntitiesFromTable = [&](const std::vector<std::shared_ptr<DayZ::Entity>>& entities, 
									 const std::string& tableName) {
		for (const auto& entity : entities) {
			if (!entity || !entity->isValid()) continue;
			
			uint32_t entityId = entity->NetworkID;
			
			// Skip if already processed (deduplication)
			if (processedIds.find(entityId) != processedIds.end()) {
				continue;
			}
			
			ConsolidatedEntity consolidated_entity;
			consolidated_entity.entity = entity;
			consolidated_entity.sourceTable = tableName;
			consolidated_entity.entityId = entityId;
			
			// FIXED: Properly categorize entities based on their actual type, not source table
			// Determine if entity is alive based on actual entity type
			bool isAlive = entity->isPlayer() || entity->isZombie() || entity->isAnimal();
			bool isLoot = !isAlive; // If not alive, it's loot
			
			// Special case: Dead players and animals are loot items, not alive entities
			if (isAlive && entity->isDead) {
				isAlive = false;
				isLoot = true;
			}
			
			consolidated_entity.isLootItem = isLoot;
			consolidated_entity.isAliveEntity = isAlive;
			
			consolidated.push_back(consolidated_entity);
			processedIds.insert(entityId);
		}
	};
	
	// Consolidate from all entity tables
	if (memUpdater) {
		// Item tables (mixed content - need proper type detection)
		if (memUpdater->getItemTable()) {
			addEntitiesFromTable(memUpdater->getItemTable()->resolvedEntities, "ItemTable");
		}
		if (memUpdater->getSlowEntityTable()) {
			addEntitiesFromTable(memUpdater->getSlowEntityTable()->resolvedEntities, "SlowTable");
		}
		
		// Entity tables (mixed content - need proper type detection)
		if (memUpdater->getNearEntityTable()) {
			addEntitiesFromTable(memUpdater->getNearEntityTable()->resolvedEntities, "NearTable");
		}
		if (memUpdater->getFarEntityTable()) {
			addEntitiesFromTable(memUpdater->getFarEntityTable()->resolvedEntities, "FarTable");
		}
	}
	
	return consolidated;
}

// Extracts unique entities from consolidated collection based on type filters
std::vector<std::shared_ptr<DayZ::Entity>> DayZ::OverlayAdapter::extractUniqueEntities(
	const std::vector<ConsolidatedEntity>& consolidated, bool includeLoot, bool includeAlive)
{
	std::vector<std::shared_ptr<DayZ::Entity>> entities;
	entities.reserve(consolidated.size());
	
	for (const auto& consolidated_entity : consolidated) {
		bool shouldInclude = false;
		
		// FIXED: Improved filtering logic
		// Include if entity matches requested type(s)
		if (includeLoot && consolidated_entity.isLootItem) {
			shouldInclude = true;
		}
		if (includeAlive && consolidated_entity.isAliveEntity) {
			shouldInclude = true;
		}
		
		// Safety check: if both flags are true, include all valid entities
		if (includeLoot && includeAlive) {
			shouldInclude = true;
		}
		
		// Additional safety: if no specific filters, include everything (fallback)
		if (!includeLoot && !includeAlive) {
			shouldInclude = true;
		}
		
		if (shouldInclude) {
			entities.push_back(consolidated_entity.entity);
		}
	}
	
	return entities;
}

// Phase 4: Frustum culling optimization
// Calculates the camera's view frustum for culling
DayZ::OverlayAdapter::CameraFrustum DayZ::OverlayAdapter::calculateCameraFrustum(DayZ::Camera* camera)
{
	CameraFrustum frustum;
	
	if (!camera) {
		// Return empty frustum if no camera
		return frustum;
	}
	
	// Get window size for aspect ratio
	ImVec2 windowSize = ImGui::GetWindowSize();
	frustum.aspect = windowSize.x / windowSize.y;
	
	// More conservative FOV and distances for DayZ
	frustum.fov = 90.0f; // Conservative wider FOV to avoid over-culling
	frustum.near_dist = 1.0f; // Conservative near distance
	frustum.far_dist = 5000.0f; // Conservative far distance
	
	// Helper lambda for vector normalization
	auto normalize = [](const DMARender::Vector3& vec) {
		float length = sqrt(vec.x * vec.x + vec.y * vec.y + vec.z * vec.z);
		if (length > 0.0f) {
			return DMARender::Vector3(vec.x / length, vec.y / length, vec.z / length);
		}
		return DMARender::Vector3(0, 0, 0);
	};
	
	// Helper lambda for scalar multiplication
	auto scalarMul = [](const DMARender::Vector3& vec, float scalar) {
		return DMARender::Vector3(vec.x * scalar, vec.y * scalar, vec.z * scalar);
	};
	
	// Helper lambda for vector subtraction
	auto vectorSub = [](const DMARender::Vector3& a, const DMARender::Vector3& b) {
		return DMARender::Vector3(a.x - b.x, a.y - b.y, a.z - b.z);
	};
	
	// Calculate frustum planes using camera vectors
	DMARender::Vector3 forward = camera->InvertedViewForward;
	DMARender::Vector3 right = camera->InvertedViewRight;
	DMARender::Vector3 up = camera->InvertedViewUp;
	DMARender::Vector3 pos = camera->InvertedViewTranslation;
	
	// Normalize vectors
	forward = normalize(forward);
	right = normalize(right);
	up = normalize(up);
	
	// Calculate half-angles
	float fov_rad = frustum.fov * 3.14159f / 180.0f;
	float half_fov = fov_rad * 0.5f;
	float half_fov_h = atan(tan(half_fov) * frustum.aspect);
	
	// Calculate frustum plane normals
	float cos_fov = cos(half_fov);
	float sin_fov = sin(half_fov);
	float cos_fov_h = cos(half_fov_h);
	float sin_fov_h = sin(half_fov_h);
	
	// Left plane: points to the right of the left plane are inside
	frustum.left.normal = normalize(scalarMul(forward, cos_fov_h) + scalarMul(right, sin_fov_h));
	frustum.left.distance = frustum.left.normal.Dot(pos);
	
	// Right plane: points to the left of the right plane are inside
	frustum.right.normal = normalize(scalarMul(forward, cos_fov_h) + scalarMul(right, -sin_fov_h));
	frustum.right.distance = frustum.right.normal.Dot(pos);
	
	// Top plane: points below the top plane are inside
	frustum.top.normal = normalize(scalarMul(forward, cos_fov) + scalarMul(up, -sin_fov));
	frustum.top.distance = frustum.top.normal.Dot(pos);
	
	// Bottom plane: points above the bottom plane are inside
	frustum.bottom.normal = normalize(scalarMul(forward, cos_fov) + scalarMul(up, sin_fov));
	frustum.bottom.distance = frustum.bottom.normal.Dot(pos);
	
	// Near plane: points in front of near plane are inside
	frustum.near_plane.normal = forward;
	frustum.near_plane.distance = frustum.near_plane.normal.Dot(pos) + frustum.near_dist;
	
	// Far plane: points behind far plane are inside
	frustum.far_plane.normal = scalarMul(forward, -1.0f);
	frustum.far_plane.distance = frustum.far_plane.normal.Dot(pos) + frustum.far_dist;
	
	return frustum;
}

// Tests if a point is inside the camera frustum
bool DayZ::OverlayAdapter::isPointInFrustum(const DMARender::Vector3& point, const CameraFrustum& frustum)
{
	// Helper lambda for dot product (since Dot method is not const)
	auto dotProduct = [](const DMARender::Vector3& a, const DMARender::Vector3& b) {
		return a.x * b.x + a.y * b.y + a.z * b.z;
	};
	
	// Test against all frustum planes
	if (dotProduct(frustum.left.normal, point) - frustum.left.distance < 0) return false;
	if (dotProduct(frustum.right.normal, point) - frustum.right.distance < 0) return false;
	if (dotProduct(frustum.top.normal, point) - frustum.top.distance < 0) return false;
	if (dotProduct(frustum.bottom.normal, point) - frustum.bottom.distance < 0) return false;
	if (dotProduct(frustum.near_plane.normal, point) - frustum.near_plane.distance < 0) return false;
	if (dotProduct(frustum.far_plane.normal, point) - frustum.far_plane.distance < 0) return false;

	return true;
}

// Tests if an entity is inside the camera frustum
bool DayZ::OverlayAdapter::isEntityInFrustum(const std::shared_ptr<DayZ::Entity>& entity, const CameraFrustum& frustum)
{
	if (!entity || !entity->FutureVisualStatePtr) {
		return false;
	}
	
	DMARender::Vector3 entityPos = entity->FutureVisualStatePtr->position;
	
	// Test entity position against frustum
	// For better accuracy, we could test a bounding sphere, but point test is faster
	return isPointInFrustum(entityPos, frustum);
}

// Culls entities that are outside the camera frustum
std::vector<std::shared_ptr<DayZ::Entity>> DayZ::OverlayAdapter::frustumCullEntities(
	const std::vector<std::shared_ptr<DayZ::Entity>>& entities, const CameraFrustum& frustum)
{
	std::vector<std::shared_ptr<DayZ::Entity>> visibleEntities;
	visibleEntities.reserve(entities.size());
	
	for (const auto& entity : entities) {
		if (isEntityInFrustum(entity, frustum)) {
			visibleEntities.push_back(entity);
		}
	}
	
	return visibleEntities;
}

// Helper function to extract positions from entities for batching
std::vector<DMARender::Vector3> DayZ::OverlayAdapter::extractEntityPositions(const std::vector<std::shared_ptr<DayZ::Entity>>& entities)
{
	std::vector<DMARender::Vector3> positions;
	positions.reserve(entities.size() * 2); // Reserve space for both origin and top positions
	
	for (const auto& ent : entities) {
		if (!ent || !ent->isValid() || ent->isDead) {
			// Add dummy positions for invalid entities to maintain index alignment
			positions.push_back(DMARender::Vector3(0, 0, 0));
			positions.push_back(DMARender::Vector3(0, 0, 0));
			continue;
		}
		
		auto originPos = ent->FutureVisualStatePtr->position;
		float entHeight = ent->isAnimal() ? 1.0f : 1.8f;
		auto topPos = originPos + DMARender::Vector3(0, entHeight, 0);
		
		positions.push_back(originPos);
		positions.push_back(topPos);
	}
	
	return positions;
}

//DayZ::OverlayAdapter::OverlayAdapter(DayZ::MemoryUpdater* memUpdater, std::shared_ptr<DMARender::RenderBridge> renderBridge)
//{
//	this->memUpdater = memUpdater;
//}

void DayZ::OverlayAdapter::DrawOverlay()
{
	// Clear text positions for DayZ2-style overlap prevention (start fresh each frame)
	clearTextPositions();

	// Update skeleton cache (clean up expired entries)
	SkeletonESP::UpdateSkeletonCache();

	// Draw crosshair (if enabled)
	drawCrosshair();

	// Draw FPS counter and player count in top right corner
	auto io = ImGui::GetIO();
	const float screenWidth = io.DisplaySize.x;
	const float screenHeight = io.DisplaySize.y;

	// Calculate player count from all entity tables
	int playerCount = 0;
	if (memUpdater) {
		// Count players from near entities
		auto nearTable = memUpdater->getNearEntityTable();
		if (nearTable) {
			for (const auto& entity : nearTable->resolvedEntities) {
				if (entity && entity->isValid() && entity->isPlayer() && !entity->isDead) {
					playerCount++;
				}
			}
		}

		// Count players from far entities
		auto farTable = memUpdater->getFarEntityTable();
		if (farTable) {
			for (const auto& entity : farTable->resolvedEntities) {
				if (entity && entity->isValid() && entity->isPlayer() && !entity->isDead) {
					playerCount++;
				}
			}
		}
	}

	// Display FPS and player count in one line in top right corner
	std::string combinedText = "FPS: " + std::to_string(static_cast<int>(io.Framerate)) + " | Players: " + std::to_string(playerCount);

	// Get dynamic positioning settings from renderBridge (with fallback defaults)
	float horizontalPadding = 485.0f;  // Default fallback
	float verticalPadding = 27.0f;     // Default fallback
	
	if (renderBridge) {
		horizontalPadding = renderBridge->getFpsCounterHorizontalPadding();
		verticalPadding = renderBridge->getFpsCounterVerticalPadding();
	}
	
	// Get dynamic font size from settings (default 20.0f if bridge not available)
	float fontSize = 20.0f;
	if (renderBridge) {
		fontSize = renderBridge->getFpsCounterFontSize();
	}

	// Calculate text width for right alignment
	ImVec2 textSize = ImGui::CalcTextSize(combinedText.c_str());

	// Position for combined text
	DMARender::Vector2 textPos;
	textPos.x = screenWidth - textSize.x - horizontalPadding;
	textPos.y = verticalPadding;

	// Draw the text with white color and full opacity for crisp rendering
	ImU32 textColor = IM_COL32(255, 255, 255, 255);  // Full opacity for better clarity

	// Push font for high-quality rendering (uses the master font with antialiasing)
	if (playerFont) {
		ImGui::PushFont(playerFont);
	}

	drawText(combinedText, textPos, fontSize, textColor);

	// Pop font if we pushed one
	if (playerFont) {
		ImGui::PopFont();
	}

	// Early exit check - do this BEFORE any expensive operations
	bool itemESPEnabled = renderBridge->isItemESPEnabled();
	bool anyPlayerESPEnabled = renderBridge->shouldShowPlayerNameFUSER() ||
							   renderBridge->shouldShowPlayerDistanceFUSER() ||
							   renderBridge->shouldShowPlayerHandFUSER() ||
							   renderBridge->shouldShowPlayerBoxFUSER();
	bool anyEntityESPEnabled = renderBridge->shouldShowZombiesFUSER() ||
							   renderBridge->shouldShowAnimalsFUSER();
	bool debugEnabled = renderBridge->shouldShowDebugFUSER();

	// If absolutely nothing is enabled, skip processing
	if (!itemESPEnabled && !anyPlayerESPEnabled && !anyEntityESPEnabled && !debugEnabled) {
		return;
	}





	// Skip ALL processing including camera access when ESP is disabled
	// This prevents DMA reads and background processing that cause lag spikes
	if (!itemESPEnabled && !anyPlayerESPEnabled && !anyEntityESPEnabled && !debugEnabled) {
		return; // INSTANT RETURN - No DMA access, no lag spikes!
	}



	auto camera = memUpdater->getCamera();
	if (!camera) return;

	// Only sync LootListManager if item ESP is enabled
	if (itemESPEnabled) {
		static auto lastSync = std::chrono::steady_clock::now();
		auto now = std::chrono::steady_clock::now();
		if (std::chrono::duration_cast<std::chrono::seconds>(now - lastSync).count() >= 5) {
			auto lootManager = renderBridge->getLootListManager();
			if (lootManager) {
				// Update Entity system with current rare items from LootListManager
				DayZ::Entity::setRareItems(lootManager->getRareItems());
			}
			lastSync = now;
		}
	}





	// Phase 4: Simple culling optimization implemented in WorldToScreenBatch function
	// Provides performance benefits by eliminating off-screen and behind-camera entities early

	// Draw loot items (only if item ESP is enabled) - back to original approach
	if (itemESPEnabled) {
		drawLoot(camera.get(), memUpdater->getItemTable()->resolvedEntities);
		drawLoot(camera.get(), memUpdater->getSlowEntityTable()->resolvedEntities);
		drawLoot(camera.get(), memUpdater->getNearEntityTable()->resolvedEntities);
		drawLoot(camera.get(), memUpdater->getFarEntityTable()->resolvedEntities);
	}

	// DayZ2-style approach - no caching needed

	// Draw alive entities (only if any player/entity ESP is enabled) - back to original approach
	if (anyPlayerESPEnabled || anyEntityESPEnabled) {
		drawAliveEntities(camera.get(), memUpdater->getNearEntityTable()->resolvedEntities, memUpdater->getScoreboard().get());
		drawAliveEntities(camera.get(), memUpdater->getFarEntityTable()->resolvedEntities, memUpdater->getScoreboard().get());
	}

	// Draw personal health display (your own health stats)
	if (renderBridge && renderBridge->isShowPersonalHealth()) {
		// Find the local player by name
		std::string PlayerNameYES = renderBridge->shouldPlayerName();
		std::vector<std::string> MainPlayerName = { PlayerNameYES };

		std::shared_ptr<DayZ::Entity> localPlayer = nullptr;

		// Search for local player in near entities first (most likely location)
		auto nearEntities = memUpdater->getNearEntityTable();
		if (nearEntities) {
			for (const auto& ent : nearEntities->resolvedEntities) {
				if (ent && ent->isValid() && ent->isPlayer() && !ent->isDead) {
					auto ident = ent->getPlayerIdentity(memUpdater->getScoreboard().get());
					if (ident && ident->PlayerName) {
						bool isMainPlayer = std::find(MainPlayerName.begin(), MainPlayerName.end(), ident->PlayerName->value) != MainPlayerName.end();
						if (isMainPlayer) {
							localPlayer = ent;
							break;
						}
					}
				}
			}
		}

		// If not found in near entities, search far entities
		if (!localPlayer) {
			auto farEntities = memUpdater->getFarEntityTable();
			if (farEntities) {
				for (const auto& ent : farEntities->resolvedEntities) {
					if (ent && ent->isValid() && ent->isPlayer() && !ent->isDead) {
						auto ident = ent->getPlayerIdentity(memUpdater->getScoreboard().get());
						if (ident && ident->PlayerName) {
							bool isMainPlayer = std::find(MainPlayerName.begin(), MainPlayerName.end(), ident->PlayerName->value) != MainPlayerName.end();
							if (isMainPlayer) {
								localPlayer = ent;
								break;
							}
						}
					}
				}
			}
		}

		// Draw personal health if local player found
		if (localPlayer) {
			float scale = renderBridge->getPersonalHealthScale();
			bool showBackground = renderBridge->isPersonalHealthBackground();

			if (renderBridge->isUseIndividualPositioning()) {
				// Individual positioning mode
				float healthX = renderBridge->getHealthIndicatorX();
				float healthY = renderBridge->getHealthIndicatorY();
				float bloodX = renderBridge->getBloodIndicatorX();
				float bloodY = renderBridge->getBloodIndicatorY();
				float shockX = renderBridge->getShockIndicatorX();
				float shockY = renderBridge->getShockIndicatorY();
				float statusX = renderBridge->getStatusIndicatorX();
				float statusY = renderBridge->getStatusIndicatorY();

				// Get individual indicator toggles
				bool showHealth = renderBridge->isShowHealthIndicator();
				bool showBlood = renderBridge->isShowBloodIndicator();
				bool showShock = renderBridge->isShowShockIndicator();
				bool showStatus = renderBridge->isShowStatusIndicator();

				// Get formatting options
				bool showLabels = renderBridge->isShowHealthLabels();
				bool useCustomFont = renderBridge->isUseCustomFont();

				PersonalHealthDisplay::DrawPersonalHealthIndividual(localPlayer, scale, showBackground,
																  healthX, healthY, bloodX, bloodY,
																  shockX, shockY, statusX, statusY,
																  showHealth, showBlood, showShock, showStatus,
																  showLabels, useCustomFont);
			} else {
				// Original grouped positioning mode
				int position = renderBridge->getPersonalHealthPosition();
				PersonalHealthDisplay::DrawPersonalHealth(localPlayer, position, scale, showBackground);
			}
		}
	}

	// Draw player list if enabled (same as radar functionality)
	if (renderBridge && renderBridge->shouldShowPlayerList()) {
		drawPlayerList(camera.get(), memUpdater->getScoreboard().get());
	}

	// Draw server player list if enabled (same as radar functionality)
	if (renderBridge && renderBridge->shouldShowServerPlayerList()) {
		drawServerPlayerList(memUpdater->getScoreboard());
	}

	// Draw debug information if enabled - back to original approach
	if (debugEnabled) {
		drawDebugInformation(camera.get(), memUpdater->getNearEntityTable()->resolvedEntities);
	}



}

void DayZ::OverlayAdapter::drawPlayerList(DayZ::Camera* camera, Scoreboard* scoreboard) {
	static bool includeSlowEntities = false; // Checkbox-Status

	// Create Local Player List window with proper sizing
	ImGui::SetNextWindowSize(ImVec2(400, 500), ImGuiCond_FirstUseEver);
	ImGui::Begin("Local Player List", nullptr, ImGuiWindowFlags_None);

	// Add checkbox for SlowEntityTable, experimental to find invisible admins
	ImGui::Checkbox("Include SlowEntityTable", &includeSlowEntities);
	if (ImGui::IsItemHovered()) {
		ImGui::SetTooltip("Include players from slow entity table (experimental - may show invisible admins)");
	}

	ImGui::Spacing();

	// Collect slow entity IDs if enabled
	std::set<uint32_t> slowEntityIDs;
	if (includeSlowEntities) {
		for (const auto& slowEntity : memUpdater->getSlowEntityTable()->resolvedEntities) {
			if (slowEntity->isPlayer() && slowEntity->isValid()) {
				slowEntityIDs.insert(slowEntity->NetworkID);
			}
		}
	}

	// Create table for player list with proper sizing
	if (ImGui::BeginTable("PlayerTable", 2, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY, ImVec2(0, 400))) {
		ImGui::TableSetupColumn("Distance", ImGuiTableColumnFlags_WidthFixed, 100.0f);
		ImGui::TableSetupColumn("Player Name", ImGuiTableColumnFlags_WidthStretch);
		ImGui::TableHeadersRow();

		// Count entities for debugging
		int totalNearEntities = memUpdater->getNearEntityTable()->resolvedEntities.size();
		int totalFarEntities = memUpdater->getFarEntityTable()->resolvedEntities.size();
		int playerCount = 0;

		// Lambda to process entities
		auto processEntities = [&](const std::vector<std::shared_ptr<DayZ::Entity>>& entities) {
			for (const auto& ent : entities) {
				if (!ent || !ent->isValid() || !ent->isPlayer() || ent->isDead) continue;

				auto ident = ent->getPlayerIdentity(scoreboard);
				if (!ident || !ident->PlayerName || !ident->PlayerName->value) continue;

				const char* playerName = ident->PlayerName->value;
				uint32_t networkID = ent->NetworkID;

				// Calculate distance (simplified - using position if available)
				float distance = 0.0f;
				if (ent->FutureVisualStatePtr && camera) {
					// Calculate distance from camera position
					auto pos = ent->FutureVisualStatePtr->position;
					auto camPos = camera->InvertedViewTranslation;
					distance = sqrt(pow(pos.x - camPos.x, 2) + pow(pos.y - camPos.y, 2) + pow(pos.z - camPos.z, 2));
				}

				// Check if this is a slow entity
				bool isSlowEntity = slowEntityIDs.count(networkID) > 0;
				ImU32 rowColor = isSlowEntity ? IM_COL32(255, 255, 0, 255) : IM_COL32(255, 255, 255, 255);

				ImGui::TableNextRow();
				ImGui::PushStyleColor(ImGuiCol_Text, rowColor);

				// Distance column
				ImGui::TableNextColumn();
				ImGui::Text("%.0fm", distance);

				// Player Name column
				ImGui::TableNextColumn();
				ImGui::Text("%s", playerName);

				ImGui::PopStyleColor();
				playerCount++;
			}
		};

		// Process both near and far entity tables
		processEntities(memUpdater->getNearEntityTable()->resolvedEntities);
		processEntities(memUpdater->getFarEntityTable()->resolvedEntities);

		// If no players found, show debug info
		if (playerCount == 0) {
			ImGui::TableNextRow();
			ImGui::TableNextColumn();
			ImGui::Text("Debug:");
			ImGui::TableNextColumn();
			ImGui::Text("Near: %d, Far: %d entities", totalNearEntities, totalFarEntities);
		}

		ImGui::EndTable();
	}
	ImGui::End();
}

void DayZ::OverlayAdapter::drawServerPlayerList(std::shared_ptr<DayZ::Scoreboard> scoreboard) {
	std::unordered_set<int> seenNetworkIDs;

	// Create Server Player List window with proper sizing
	ImGui::SetNextWindowSize(ImVec2(500, 600), ImGuiCond_FirstUseEver);
	ImGui::Begin("Server Player List", nullptr, ImGuiWindowFlags_None);

	ImGui::Text("Click Steam ID to Copy Profile Link to Clipboard");
	ImGui::Text("Right-click player to exclude/include from ESP/Radar");
	ImGui::Text("Middle-click player to add/remove from Admin List");
	ImGui::Spacing();

	int rowIndex = 1;
	int totalIdentities = scoreboard ? scoreboard->resolvedIdentities.size() : 0;

	if (ImGui::BeginTable("ServerPlayerTable", 3, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_ScrollY, ImVec2(0, 500))) {
		ImGui::TableSetupColumn("#", ImGuiTableColumnFlags_WidthFixed, 50.0f);
		ImGui::TableSetupColumn("Player Name", ImGuiTableColumnFlags_WidthStretch);
		ImGui::TableSetupColumn("Steam ID", ImGuiTableColumnFlags_WidthFixed, 150.0f);
		ImGui::TableHeadersRow();

		if (scoreboard) {
			for (const auto& identity : scoreboard->resolvedIdentities) {
				if (!identity) continue;

				// Validation
				if (identity->NetworkID <= 9999 ||
					!identity->SteamID || !identity->SteamID->value ||
					strncmp(identity->SteamID->value, "7656", 4) != 0 ||
					!identity->PlayerName || !identity->PlayerName->value ||
					!seenNetworkIDs.insert(identity->NetworkID).second) {
					continue;
				}

				const char* playerName = identity->PlayerName->value;
				const char* steamID = identity->SteamID->value;

				// Check if player is excluded or admin
				bool isExcluded = renderBridge && renderBridge->isPlayerExcluded(std::string(steamID));
				bool isAdmin = renderBridge && renderBridge->isPlayerAdmin(std::string(steamID));

				// Set row color: red for excluded, green for admin, white for normal
				ImU32 rowColor = isExcluded ? IM_COL32(255, 100, 100, 255) :
								(isAdmin ? IM_COL32(0, 255, 0, 255) : IM_COL32(255, 255, 255, 255));

				ImGui::TableNextRow();
				ImGui::PushStyleColor(ImGuiCol_Text, rowColor);

				// Row number
				ImGui::TableNextColumn();
				ImGui::Text("%d", rowIndex++);

				// Player Name with exclusion and admin indicators
				ImGui::TableNextColumn();
				if (isExcluded) {
					ImGui::Text("[EXCLUDED] %s", playerName);
				} else if (isAdmin) {
					ImGui::Text("[ADMIN] %s", playerName);
				} else {
					ImGui::Text("%s", playerName);
				}

				// Steam ID (clickable)
				ImGui::TableNextColumn();
				if (ImGui::Selectable(steamID, false, ImGuiSelectableFlags_SpanAllColumns)) {
					// Copy Steam profile URL to clipboard
					std::string profileUrl = "https://steamcommunity.com/profiles/" + std::string(steamID);
					ImGui::SetClipboardText(profileUrl.c_str());
				}
				if (ImGui::IsItemHovered()) {
					ImGui::SetTooltip("Left-click to copy Steam profile URL\nRight-click to exclude/include from ESP/Radar\nMiddle-click to add/remove from Admin List");
				}

				// Handle right-click context menu (exclusion)
				if (ImGui::IsItemClicked(ImGuiMouseButton_Right)) {
					if (renderBridge) {
						if (isExcluded) {
							renderBridge->removeExcludedPlayer(std::string(steamID));
						} else {
							renderBridge->addExcludedPlayer(std::string(steamID));
						}
					}
				}

				// Handle middle-click context menu (admin list)
				if (ImGui::IsItemClicked(ImGuiMouseButton_Middle)) {
					if (renderBridge) {
						if (isAdmin) {
							renderBridge->removeAdminPlayer(std::string(steamID));
						} else {
							renderBridge->addAdminPlayer(std::string(steamID));
						}
					}
				}

				ImGui::PopStyleColor();
			}
		}

		// If no players found, show debug info
		if (rowIndex == 1) {
			ImGui::TableNextRow();
			ImGui::TableNextColumn();
			ImGui::Text("Debug:");
			ImGui::TableNextColumn();
			ImGui::Text("Total identities: %d", totalIdentities);
			ImGui::TableNextColumn();
			ImGui::Text("Scoreboard: %s", scoreboard ? "Valid" : "NULL");
		}

		ImGui::EndTable();
	}

	ImGui::Spacing();
	ImGui::Text("Total Players: %d", rowIndex - 1);
	ImGui::End();
}

void DayZ::OverlayAdapter::createFonts()
{
	// Get DPI scale factor
	float dpiScale = ImGui::GetIO().DisplayFramebufferScale.x;
	if (dpiScale <= 0.0f) dpiScale = 1.0f; // Fallback to 1.0 if invalid

	// Debug: Log DPI values for troubleshooting
	std::cout << "[FONT DEBUG] Using DPI scale: " << dpiScale << std::endl;

	// Get base font size from bridge with DPI scaling
	float baseFontSizeFromBridge = 28.0f; // Default fallback
	if (renderBridge) {
		try {
			baseFontSizeFromBridge = renderBridge->getBaseFontSize();
		}
		catch (...) {
			std::cout << "[FONT DEBUG] Failed to get base font size from bridge, using default 28px" << std::endl;
		}
	}
	
	// Use user-configurable base font size with DPI scaling for better clarity and detail
	float baseTextSize = baseFontSizeFromBridge * dpiScale;  // User's selected base size with DPI scaling

	// Ensure minimum readable size
	if (baseTextSize < 8.0f) baseTextSize = 8.0f;   // Minimum for readability
	if (baseTextSize > 96.0f) baseTextSize = 96.0f; // Maximum reasonable size

	// QUALITY-ENHANCED FONT SETTINGS: Clean with improved quality
	int dynamicOversampleH = 2;  // Quality mode - minimal oversampling for smoothness
	int dynamicOversampleV = 1;   // Quality mode - minimal vertical oversampling
	float dynamicRasterizerMultiply = 1.1f; // Quality mode - slight brightness boost for clarity
	bool dynamicPixelSnap = true; // Quality mode - pixel perfect alignment
	int dynamicAtlasSize = 2048; // Quality mode - higher resolution atlas for better detail

	// Get font quality settings from bridge if available - with safety checks
	if (renderBridge) {
		try {
			int bridgeOversampleH = renderBridge->getFontOversampleH();
			int bridgeOversampleV = renderBridge->getFontOversampleV();
			float bridgeRasterizerMultiply = renderBridge->getFontRasterizerMultiply();
			bool bridgePixelSnap = renderBridge->getFontPixelSnap();
			int bridgeAtlasSize = renderBridge->getFontAtlasSize();
			
			// Safety validation - only use values if they're in safe ranges
			if (bridgeOversampleH >= 1 && bridgeOversampleH <= 32) {
				dynamicOversampleH = bridgeOversampleH;
			}
			if (bridgeOversampleV >= 1 && bridgeOversampleV <= 16) {
				dynamicOversampleV = bridgeOversampleV;
			}
			if (bridgeRasterizerMultiply >= 0.5f && bridgeRasterizerMultiply <= 2.0f) {
				dynamicRasterizerMultiply = bridgeRasterizerMultiply;
			}
			
			// Apply pixel snap and atlas size settings
			dynamicPixelSnap = bridgePixelSnap;
			if (bridgeAtlasSize == 1024 || bridgeAtlasSize == 2048 || bridgeAtlasSize == 4096) {
				dynamicAtlasSize = bridgeAtlasSize;
			}
		}
		catch (...) {
			// Use defaults if bridge access fails
		}
	}

	// QUALITY-ENHANCED FONT CONFIGURATION: Clean with improved visual quality
	ImFontConfig config;
	config.OversampleH = dynamicOversampleH;  // 2 - controlled oversampling for smooth edges
	config.OversampleV = dynamicOversampleV;  // 1 - minimal vertical oversampling
	config.GlyphExtraSpacing.x = 0.0f; // Natural spacing for better readability
	config.GlyphOffset.y = 0.0f;     // Perfect alignment
	config.PixelSnapH = dynamicPixelSnap;       // Pixel perfect alignment
	config.RasterizerMultiply = dynamicRasterizerMultiply; // Enhanced brightness for clarity
	config.FontDataOwnedByAtlas = false; // Better memory management for quality
	
	// Add Cyrillic support to fix ?????? issue with Russian/Cyrillic text
	config.GlyphRanges = ImGui::GetIO().Fonts->GetGlyphRangesCyrillic();

	// Get pixel snap setting from bridge if available
	if (renderBridge) {
		try {
			config.PixelSnapH = renderBridge->getFontPixelSnap();
		}
		catch (...) {
			// Use default if bridge access fails
			std::cout << "[FONT DEBUG] Failed to get pixel snap setting, using default" << std::endl;
		}
	}

	// DYNAMIC FONT SELECTION: Get user's selected font from bridge with dynamic detection
	std::vector<std::string> fontFiles;
	std::vector<std::string> fontNames;
	int selectedFontIndex = 0; // Default to first available font

	// Get dynamic font list from bridge
	if (renderBridge) {
		try {
			fontFiles = renderBridge->getAvailableFontFiles();
			fontNames = renderBridge->getAvailableFontNames();
			selectedFontIndex = renderBridge->getSelectedFontIndex();
			
			// Ensure valid index for dynamic font list
			if (selectedFontIndex < 0 || selectedFontIndex >= (int)fontFiles.size()) {
				selectedFontIndex = 0; // Reset to first available font
				if (!fontFiles.empty()) {
					renderBridge->setSelectedFontIndex(selectedFontIndex);
				}
			}
			
			std::cout << "[FONT DEBUG] Using dynamic font detection - found " << fontFiles.size() << " fonts" << std::endl;
			if (selectedFontIndex < (int)fontNames.size()) {
				std::cout << "[FONT DEBUG] Selected font: " << fontNames[selectedFontIndex] << " (" << fontFiles[selectedFontIndex] << ")" << std::endl;
			}
		}
		catch (...) {
			std::cout << "[FONT DEBUG] Dynamic font detection failed, using fallback fonts" << std::endl;
			// Fallback to basic reliable fonts
			fontFiles = {"arial.ttf", "calibri.ttf", "tahoma.ttf", "times.ttf", "verdana.ttf"};
			fontNames = {"Arial", "Calibri", "Tahoma", "Times New Roman", "Verdana"};
			selectedFontIndex = 1; // Default to Calibri
		}
	} else {
		// No bridge available, use basic fallback fonts
		fontFiles = {"arial.ttf", "calibri.ttf", "tahoma.ttf", "times.ttf", "verdana.ttf"};
		fontNames = {"Arial", "Calibri", "Tahoma", "Times New Roman", "Verdana"};
		selectedFontIndex = 1; // Default to Calibri
	}

	// Build font paths array with selected font first, then fallbacks
	std::vector<std::string> fontPaths;
	if (selectedFontIndex < (int)fontFiles.size()) {
		// Use the new method that finds the actual location of the selected font
		try {
			std::string selectedFontPath = renderBridge->getSelectedFontFullPath();
			fontPaths.push_back(selectedFontPath);
			std::cout << "[FONT DEBUG] Using selected font path: " << selectedFontPath << std::endl;
		}
		catch (...) {
			// Fallback to Windows Fonts directory if the new method fails
			fontPaths.push_back("C:\\Windows\\Fonts\\" + fontFiles[selectedFontIndex]);
			std::cout << "[FONT DEBUG] Failed to get full path, using fallback: C:\\Windows\\Fonts\\" << fontFiles[selectedFontIndex] << std::endl;
		}
	}

	// Add essential fallback fonts if not already selected
	for (const std::string& fallbackFile : {"calibri.ttf", "arial.ttf", "tahoma.ttf"}) {
		std::string fallbackPath = "C:\\Windows\\Fonts\\" + fallbackFile;
		bool alreadyAdded = false;
		for (const auto& existingPath : fontPaths) {
			if (existingPath == fallbackPath) {
				alreadyAdded = true;
				break;
			}
		}
		if (!alreadyAdded) {
			fontPaths.push_back(fallbackPath);
		}
	}

	// Initialize fonts to nullptr for safety
	lootFont = nullptr;
	lootFontFar = nullptr;
	playerFont = nullptr;

	// Load selected font with fallbacks and crash protection
	ImFont* masterFont = nullptr;
	for (const std::string& fontPath : fontPaths) {
		try {
			// Additional safety check before attempting to load
			std::cout << "[FONT DEBUG] Attempting to load font: " << fontPath << std::endl;
			
			// Validate the font file first if we have bridge access
			bool fontIsValid = true;
			if (renderBridge) {
				try {
					fontIsValid = renderBridge->isValidFontFile(fontPath);
					if (!fontIsValid) {
						std::cout << "[FONT DEBUG] Font validation failed, skipping: " << fontPath << std::endl;
						continue;
					}
				}
				catch (...) {
					std::cout << "[FONT DEBUG] Font validation threw exception, skipping: " << fontPath << std::endl;
					continue;
				}
			}
			
			// Try to load the font with additional error handling
			ImFont* tempFont = ImGui::GetIO().Fonts->AddFontFromFileTTF(fontPath.c_str(), baseTextSize, &config);
			if (tempFont) {
				masterFont = tempFont;
				std::cout << "[FONT DEBUG] Successfully loaded font: " << fontPath << std::endl;
				break;
			} else {
				std::cout << "[FONT DEBUG] Font loading returned null: " << fontPath << std::endl;
			}
		}
		catch (const std::exception& e) {
			std::cout << "[FONT DEBUG] Exception loading font " << fontPath << ": " << e.what() << std::endl;
			continue;
		}
		catch (...) {
			std::cout << "[FONT DEBUG] Unknown exception loading font: " << fontPath << std::endl;
			continue;
		}
	}

	// Use the same master font instance for all text types to ensure identical antialiasing
	lootFont = masterFont;
	lootFontFar = masterFont;
	playerFont = masterFont;

	// Ensure all fonts are loaded, fallback to default if needed
	if (!masterFont) {
		std::cout << "[FONT DEBUG] No custom fonts loaded, using default font" << std::endl;
		masterFont = ImGui::GetIO().Fonts->AddFontDefault();
		lootFont = masterFont;
		lootFontFar = masterFont;
		playerFont = masterFont;
	}

	// QUALITY-ENHANCED FONT ATLAS BUILD: Higher quality with clean rendering
		ImGuiIO& io = ImGui::GetIO();
	io.Fonts->TexDesiredWidth = dynamicAtlasSize;  // Higher resolution atlas for better detail
	io.Fonts->TexGlyphPadding = 1;     // Minimal padding for quality without artifacts
	io.Fonts->Flags |= ImFontAtlasFlags_NoPowerOfTwoHeight; // Allow non-power-of-2 for better packing
	io.Fonts->Flags |= ImFontAtlasFlags_NoMouseCursors; // Optimize atlas for text only
	
	// Get font atlas size from bridge if available
	if (renderBridge) {
		try {
			int bridgeAtlasSize = renderBridge->getFontAtlasSize();
			// Safety validation - only use specific valid sizes
			if (bridgeAtlasSize == 1024 || bridgeAtlasSize == 2048 || bridgeAtlasSize == 4096) {
				io.Fonts->TexDesiredWidth = bridgeAtlasSize;
			} else {
				std::cout << "[FONT DEBUG] Invalid atlas size " << bridgeAtlasSize << ", using default 1024" << std::endl;
			}
		}
		catch (...) {
			// Use default if bridge access fails
			std::cout << "[FONT DEBUG] Failed to get atlas size, using default 1024" << std::endl;
		}
	}
	
	// Build font atlas with ultra-clean settings
	try {
		std::cout << "[FONT DEBUG] Building QUALITY-ENHANCED font atlas..." << std::endl;
		std::cout << "[FONT DEBUG] Settings: OversampleH=" << dynamicOversampleH << ", OversampleV=" << dynamicOversampleV << std::endl;
		std::cout << "[FONT DEBUG] Settings: RasterizerMultiply=" << dynamicRasterizerMultiply << ", PixelSnap=" << (dynamicPixelSnap ? "ON" : "OFF") << std::endl;
		std::cout << "[FONT DEBUG] Settings: AtlasSize=" << dynamicAtlasSize << ", GlyphPadding=" << io.Fonts->TexGlyphPadding << std::endl;
		std::cout << "[FONT DEBUG] Settings: BaseFontSize=" << baseFontSizeFromBridge << "px, DPI=" << dpiScale << ", FinalSize=" << baseTextSize << "px for enhanced clarity" << std::endl;
		io.Fonts->Build();
		std::cout << "[FONT DEBUG] QUALITY-ENHANCED font atlas built successfully - clean with improved quality!" << std::endl;
	}
	catch (...) {
		std::cout << "[FONT DEBUG] Font atlas build failed, using fallback" << std::endl;
		// If font atlas build fails, clear and use defaults
		io.Fonts->Clear();
		lootFont = io.Fonts->AddFontDefault();
		lootFontFar = io.Fonts->AddFontDefault();
		playerFont = io.Fonts->AddFontDefault();
		io.Fonts->Build();
	}
}

void DayZ::OverlayAdapter::drawAliveEntities(DayZ::Camera* camera, const std::vector<std::shared_ptr<DayZ::Entity>>& entities, DayZ::Scoreboard* scoreboard)
{
	// Increment frame counter for caching
	currentFrame++;

	// Clear expired caches at the start of each frame
	clearExpiredNameCache();
	clearExpiredZombieNameCache();
	clearExpiredAnimalNameCache();
	clearExpiredPositionCache();
	
	// Clear text positions at the start of each frame to prevent overlap accumulation
	clearTextPositions();

	if (!renderBridge) return; // If the render bridge is not initialized, return

	bool showZombies = renderBridge->shouldShowZombiesFUSER();
	bool showZombieBox = renderBridge->shouldShowZombieBoxFUSER();
	bool showZombieName = renderBridge->shouldShowZombieNameFUSER();
	bool showZombieDistance = renderBridge->shouldShowZombieDistanceFUSER();
	bool showAnimals = renderBridge->shouldShowAnimalsFUSER();
	bool showAnimalBox = renderBridge->shouldShowAnimalBoxFUSER();
	bool showAnimalName = renderBridge->shouldShowAnimalNameFUSER();
	bool showPlayerInfoesp = renderBridge->shouldShowPlayerInfoesp();
	bool showPlayerName = renderBridge->shouldShowPlayerNameFUSER();
	bool showPlayerDistance = renderBridge->shouldShowPlayerDistanceFUSER();
	bool showPlayerHand = renderBridge->shouldShowPlayerHandFUSER();
	bool showPlayerBox = renderBridge->shouldShowPlayerBoxFUSER();
	bool showPlayerSkeleton = renderBridge->isPlayerSkeletonEnabled();
	bool showZombieSkeleton = renderBridge->isZombieSkeletonEnabled();
	int ZombieDistance = renderBridge->shouldZombieDistanceFUSER();
	int animalMaxDistance = renderBridge->getAnimalMaxDistance();
	int playerMaxDistance = renderBridge->getPlayerMaxDistance();

	// Early exit if no alive entity features are enabled
	bool anyPlayerFeatureEnabled = showPlayerName || showPlayerDistance || showPlayerHand || showPlayerBox || showPlayerSkeleton;
	bool anyZombieFeatureEnabled = showZombies || showZombieBox || showZombieName || showZombieDistance;
	bool anyZombieSkeletonEnabled = showZombieSkeleton;
	if (!anyZombieFeatureEnabled && !anyZombieSkeletonEnabled && !showAnimals && !anyPlayerFeatureEnabled) {
		return; // Skip all processing if nothing is enabled
	}

	std::string PlayerNameYES = renderBridge->shouldPlayerName();

	std::vector<std::string> MainPlayerName = { PlayerNameYES };

	// Phase 3: Entity Consolidation - TEMPORARILY DISABLED due to filtering issues
	// std::vector<ConsolidatedEntity> consolidatedEntities = consolidateEntityTables();
	// std::vector<std::shared_ptr<DayZ::Entity>> uniqueAliveEntities = extractUniqueEntities(consolidatedEntities, false, true);

	// PHASE 1 & 2: WorldToScreen and Distance-based Batching Optimization
	// Extract all positions at once for batch processing
	std::vector<DMARender::Vector3> allPositions = extractEntityPositions(entities);
	std::vector<BatchedScreenPosition> batchedResults = WorldToScreenBatch(camera, allPositions);
	
	// Create distance-sorted batch for LOD rendering
	std::vector<EntityDistanceInfo> distanceSortedEntities = createDistanceSortedBatch(entities, batchedResults);

	for (const auto& entityInfo : distanceSortedEntities) {
		const auto& ent = entityInfo.entity;
		float dist = entityInfo.distance;



		// ESP Debug Logging - Log all player entities if enabled
		if (renderBridge && renderBridge->getEnableESPDebugLogging()) {
			logAllEntities(ent, "PlayerEntity");
		}

		// Early type checking to skip unnecessary processing
		bool isPlayer = ent->isPlayer();
		bool isZombie = ent->isZombie();
		bool isAnimal = ent->isAnimal();

		// Skip if this entity type is not enabled
		if (isPlayer && !anyPlayerFeatureEnabled) continue;
		if (isZombie && !anyZombieFeatureEnabled && !anyZombieSkeletonEnabled) continue;
		if (isAnimal && !showAnimals) continue;
		if (!isPlayer && !isZombie && !isAnimal) continue;

		// Skip excluded players
		if (isPlayer && renderBridge) {
			auto ident = ent->getPlayerIdentity(scoreboard);
			if (ident && ident->SteamID && ident->SteamID->value) {
				if (renderBridge->isPlayerExcluded(std::string(ident->SteamID->value))) {
					continue; // Skip this excluded player
				}
			}
		}

		// Distance-based quality determination
		RenderQuality quality = determineRenderQuality(dist, isPlayer, isZombie, isAnimal);
		if (quality == RenderQuality::SKIP_RENDER) continue;

		// Early distance checks
		if (dist < 3.1) continue;
		if (ent->isZombie() && dist > ZombieDistance) continue;
		if (ent->isAnimal() && dist > animalMaxDistance) continue;
		if (ent->isPlayer() && dist > playerMaxDistance) continue;

		ImU32 boxColor;
		bool isMainPlayer = false;
		std::vector<std::string> infoText;

		if (isPlayer) {
			auto ident = ent->getPlayerIdentity(scoreboard);
			if (ident && ident->PlayerName) {
				// Use cached player name if available, otherwise read from memory
				std::string currentPlayerName = "";
				if (isPlayerNameCached(ent->NetworkID)) {
					currentPlayerName = getCachedPlayerName(ent->NetworkID);
				} else if (ident->PlayerName->value) {
					currentPlayerName = std::string(ident->PlayerName->value);
				}
				
				if (!currentPlayerName.empty()) {
					isMainPlayer = std::find(MainPlayerName.begin(), MainPlayerName.end(), currentPlayerName) != MainPlayerName.end();
				}
			}
		}

		// Set colors for different entity types using customizable colors
		if (isPlayer) {
			// Check if player is admin for special coloring
			bool isAdmin = false;
			auto ident = ent->getPlayerIdentity(scoreboard);
			if (ident && ident->SteamID && ident->SteamID->value && renderBridge) {
				isAdmin = renderBridge->isPlayerAdmin(std::string(ident->SteamID->value));
			}

			// Use green color for admin players, normal color for regular players
			if (isAdmin) {
				boxColor = IM_COL32(0, 255, 0, 255); // Green for admin players
			} else {
				boxColor = renderBridge->getPlayerBoxColor();
			}
		}
		else if (isZombie) {
			boxColor = renderBridge->getZombieBoxColor();
		}
		else if (isAnimal) {
			boxColor = renderBridge->getAnimalBoxColor();
		}
		else {
			continue;
		}

		// Use pre-calculated screen positions from distance-sorted batch
		DMARender::Vector2 originW2S = entityInfo.originScreen.screenPos;
		DMARender::Vector2 topW2S = entityInfo.topScreen.screenPos;
		
		float entHeight = ent->isAnimal() ? 1.0f : 1.8f;
		float width = (originW2S.y - topW2S.y) / entHeight;

		


		// Phase 2: LOD-based rendering - Build info text based on quality level
		if (isZombie) {
			// Selective caching: Cache zombie names but not positions
			std::string zombieName = "";
			
			// Check if zombie name is cached
			if (isZombieNameCached(ent->NetworkID)) {
				zombieName = getCachedZombieName(ent->NetworkID);
			} else {
				// Read zombie name from memory and cache it
				auto entBestStr = ent->EntityTypePtr->getBestString();
				if (entBestStr) {
					zombieName = std::string(ent->EntityTypePtr->getBestString()->value);
					cacheZombieName(ent->NetworkID, zombieName);
				} else {
					zombieName = "Zombie";
					cacheZombieName(ent->NetworkID, zombieName);
				}
			}
			
			// HIGH_DETAIL: Full info, MEDIUM_DETAIL: Basic info, LOW_DETAIL: Name + distance
			if (quality == RenderQuality::HIGH_DETAIL) {
				if (showZombieName) {
					infoText.push_back(zombieName);
				}
				if (showZombieDistance) {
					infoText.push_back(std::format("{:.0f}m", dist));
				}
			}
			else if (quality == RenderQuality::MEDIUM_DETAIL) {
				if (showZombieName) {
					infoText.push_back(zombieName);
				}
				if (showZombieDistance) {
					infoText.push_back(std::format("{:.0f}m", dist));
				}
			}
			else if (quality == RenderQuality::LOW_DETAIL) {
				// Show zombie name and distance at all distances
				if (showZombieName) {
					infoText.push_back(zombieName);
				}
				if (showZombieDistance) {
					infoText.push_back(std::format("{:.0f}m", dist));
				}
			}
		}
		else if (isAnimal) {
			// Selective caching: Cache animal names but not positions
			std::string animalName = "";
			
			// Check if animal name is cached
			if (isAnimalNameCached(ent->NetworkID)) {
				animalName = getCachedAnimalName(ent->NetworkID);
			} else {
				// Read animal name from memory and cache it
				auto entBestStr = ent->EntityTypePtr->getBestString();
				if (entBestStr) {
					animalName = std::string(ent->EntityTypePtr->getBestString()->value);
					cacheAnimalName(ent->NetworkID, animalName);
				} else {
					animalName = "Animal";
					cacheAnimalName(ent->NetworkID, animalName);
				}
			}
			
			// HIGH_DETAIL: Full info, MEDIUM_DETAIL: Basic info, LOW_DETAIL: Name + distance
			if (quality == RenderQuality::HIGH_DETAIL) {
				if (showAnimalName) {
					infoText.push_back(animalName);
				}
				infoText.push_back(std::format("{:.0f}m", dist));
			}
			else if (quality == RenderQuality::MEDIUM_DETAIL) {
				if (showAnimalName) {
					infoText.push_back(animalName);
				}
				infoText.push_back(std::format("{:.0f}m", dist));
			}
			else if (quality == RenderQuality::LOW_DETAIL) {
				// Show animal name and distance at all distances
				if (showAnimalName) {
					infoText.push_back(animalName);
				}
				infoText.push_back(std::format("{:.0f}m", dist));
			}
		}
		else if (isPlayer) {
			auto ident = ent->getPlayerIdentity(scoreboard);
			
			// Selective caching: Cache player names but not positions
			std::string playerName = "";
			std::string steamId = "";
			
			if (ident && ident->SteamID && ident->SteamID->value) {
				steamId = std::string(ident->SteamID->value);
			}
			
			// Check if player name is cached
			if (isPlayerNameCached(ent->NetworkID)) {
				playerName = getCachedPlayerName(ent->NetworkID);
			} else {
				// Read player name from memory and cache it
				if (ident && ident->PlayerName && ident->PlayerName->value) {
					playerName = std::string(ident->PlayerName->value);
					cachePlayerName(ent->NetworkID, playerName, steamId);
				}
			}
			
			// HIGH_DETAIL: All info, MEDIUM_DETAIL: Name + distance, LOW_DETAIL: Name + distance (no hand item)
			if (quality == RenderQuality::HIGH_DETAIL) {
			if (!playerName.empty() && showPlayerName) {
				infoText.push_back(playerName);
			}
					if (showPlayerHand) {
			// Use cached hand item reading for better performance
			std::string handItemName = ent->getHandItemCached(memUpdater->getVMM(), memUpdater->getPid(), currentFrame);
			if (!handItemName.empty()) {
				infoText.push_back(handItemName);
			}
			// Don't add anything to infoText if no hand item - keeps display clean
		}
			if (showPlayerDistance) {
				infoText.push_back(std::format("{:.0f}m", dist));
				}
			}
			else if (quality == RenderQuality::MEDIUM_DETAIL) {
				if (!playerName.empty() && showPlayerName) {
					infoText.push_back(playerName);
				}
				if (showPlayerDistance) {
					infoText.push_back(std::format("{:.0f}m", dist));
				}
			}
			else if (quality == RenderQuality::LOW_DETAIL) {
				// LOW_DETAIL: Still show player names and distance (most important info)
				if (!playerName.empty() && showPlayerName) {
					infoText.push_back(playerName);
				}
				if (showPlayerDistance) {
					infoText.push_back(std::format("{:.0f}m", dist));
				}
			}
		}

		// Phase 2: LOD-based box rendering
		bool shouldDrawBox = false;
		// Players: Allow boxes at all quality levels (HIGH, MEDIUM, LOW)
		if (isPlayer && showPlayerBox && !isMainPlayer && 
			(quality == RenderQuality::HIGH_DETAIL || quality == RenderQuality::MEDIUM_DETAIL || quality == RenderQuality::LOW_DETAIL)) {
			shouldDrawBox = true;
		}
		// Zombies and Animals: Only HIGH and MEDIUM detail (to maintain performance)
		else if ((quality == RenderQuality::HIGH_DETAIL || quality == RenderQuality::MEDIUM_DETAIL)) {
			if (isZombie && showZombieBox) {
				shouldDrawBox = true; // Draw zombie boxes only if enabled
			}
			else if (isAnimal && showAnimalBox) {
				shouldDrawBox = true; // Draw animal boxes only if enabled
			}
		}

		if (shouldDrawBox) {
			drawBoundingBox(topW2S, originW2S, width, boxColor);
		}

		// Phase 2: DayZ2-style skeleton rendering (only HIGH_DETAIL and MEDIUM_DETAIL)
		// Performance optimization: Use configurable distance limits for skeleton rendering
		bool shouldRenderSkeleton = (quality == RenderQuality::HIGH_DETAIL || quality == RenderQuality::MEDIUM_DETAIL);
		
		// Apply configurable distance limits for skeleton rendering
		if (isPlayer && showPlayerSkeleton && dist > renderBridge->getPlayerSkeletonMaxDistance()) {
			shouldRenderSkeleton = false; // Skip skeleton rendering for players beyond max distance
		}
		if (isZombie && showZombieSkeleton && dist > renderBridge->getZombieSkeletonMaxDistance()) {
			shouldRenderSkeleton = false; // Skip skeleton rendering for zombies beyond max distance
		}
		
		// Store entity for multithreaded processing instead of rendering immediately
		if (shouldRenderSkeleton) {
			printf("[OVERLAY DEBUG] Quality check passed: %d (HIGH=%d, MEDIUM=%d)\n", 
				(int)quality, (int)RenderQuality::HIGH_DETAIL, (int)RenderQuality::MEDIUM_DETAIL);
			
			// Add entity to skeleton processing list instead of rendering immediately
			if ((isPlayer && renderBridge->isPlayerSkeletonEnabled()) || 
				(isZombie && renderBridge->isZombieSkeletonEnabled())) {
				
				// Store entity for batch processing
				skeletonProcessingEntities.push_back(ent);
			}
		}
		else {
			printf("[OVERLAY DEBUG] Quality check failed: %d (need HIGH=%d or MEDIUM=%d) - Distance: %.1f\n", 
				(int)quality, (int)RenderQuality::HIGH_DETAIL, (int)RenderQuality::MEDIUM_DETAIL, dist);
		}

		// Phase 3: Health Bar rendering (all quality levels)
		if (isPlayer && renderBridge->isPlayerHealthBarEnabled()) {
			float width = renderBridge->getHealthBarWidth();
			float height = renderBridge->getHealthBarHeight();
			float offsetY = renderBridge->getHealthBarOffsetY();
			float maxDistance = renderBridge->getHealthBarMaxDistance();
			int healthType = renderBridge->getHealthBarType();
			bool showBar = renderBridge->isShowHealthBar();
			bool showNumbers = renderBridge->isShowHealthNumbers();
			bool showAllStats = renderBridge->isShowAllHealthStats();

			// Get health bar colors
			ImU32 highColor = renderBridge->getHealthBarHighColor();
			ImU32 mediumColor = renderBridge->getHealthBarMediumColor();
			ImU32 lowColor = renderBridge->getHealthBarLowColor();
			ImU32 backgroundColor = renderBridge->getHealthBarBackgroundColor();

			HealthBarESP::DrawPlayerHealthBar(camera, ent, width, height, offsetY, maxDistance,
											highColor, mediumColor, lowColor, backgroundColor, healthType,
											showBar, showNumbers, showAllStats);
		}
		else if (isZombie && renderBridge->isZombieHealthBarEnabled()) {
			float width = renderBridge->getHealthBarWidth();
			float height = renderBridge->getHealthBarHeight();
			float offsetY = renderBridge->getHealthBarOffsetY();
			float maxDistance = renderBridge->getHealthBarMaxDistance();
			int healthType = renderBridge->getHealthBarType();
			bool showBar = renderBridge->isShowHealthBar();
			bool showNumbers = renderBridge->isShowHealthNumbers();
			bool showAllStats = renderBridge->isShowAllHealthStats();

			// Get health bar colors
			ImU32 highColor = renderBridge->getHealthBarHighColor();
			ImU32 mediumColor = renderBridge->getHealthBarMediumColor();
			ImU32 lowColor = renderBridge->getHealthBarLowColor();
			ImU32 backgroundColor = renderBridge->getHealthBarBackgroundColor();

			HealthBarESP::DrawZombieHealthBar(camera, ent, width, height, offsetY, maxDistance,
											highColor, mediumColor, lowColor, backgroundColor, healthType,
											showBar, showNumbers, showAllStats);
		}

		// Skip main player for text/box rendering (but allow skeleton and health bars)
		if (isMainPlayer) {
			continue;
		}

		// Skip text rendering if no info is enabled for this entity type
		if (isPlayer && !showPlayerName && !showPlayerDistance && !showPlayerHand)
			continue;
		if (isZombie && !showZombieName && !showZombieDistance)
			continue;
		if (isAnimal && !showAnimalName)
			continue;

		// Use the same master font as items for consistency
		ImFont* validPlayerFont = lootFont; // Use same font as items
		if (!validPlayerFont || !validPlayerFont->IsLoaded()) {
			validPlayerFont = ImGui::GetIO().Fonts->Fonts[0]; // Use default font
		}

		ImGui::PushFont(validPlayerFont);
		// Phase 2: LOD-based text size scaling
		float baseFontSize = validPlayerFont->FontSize;
		float espMultiplier = renderBridge->getESPTextSize();

		// Clamp multiplier to extended range for more control
		if (espMultiplier < 0.1f) espMultiplier = 0.1f;
		if (espMultiplier > 3.0f) espMultiplier = 3.0f;

		// Optimize scaling curve for better readability at small sizes
		float adjustedMultiplier = espMultiplier;
		if (espMultiplier < 1.0f) {
			// Soften the scaling reduction for better readability at small sizes
			adjustedMultiplier = 0.7f + (espMultiplier * 0.3f);
		}

		// Apply improved distance-based text size scaling for players
		float qualityMultiplier = 1.0f;

		// Smooth distance-based scaling for players (same as items for consistency)
		if (dist > 50.0f) {
			if (dist <= 100.0f) {
				// 50-100m: Scale from 100% to 85%
				float t = (dist - 50.0f) / 50.0f;
				qualityMultiplier = 1.0f - (t * 0.15f);
			} else if (dist <= 200.0f) {
				// 100-200m: Scale from 85% to 70%
				float t = (dist - 100.0f) / 100.0f;
				qualityMultiplier = 0.85f - (t * 0.15f);
			} else if (dist <= 400.0f) {
				// 200-400m: Scale from 70% to 55%
				float t = (dist - 200.0f) / 200.0f;
				qualityMultiplier = 0.70f - (t * 0.15f);
			} else {
				// 400m+: Minimum 55% size
				qualityMultiplier = 0.55f;
			}
		}

		float playerTextSize = baseFontSize * adjustedMultiplier * qualityMultiplier;

		// SEPARATED TEXT POSITIONING: Player name above, distance below bounding box

		// Use separate text color for players (independent from box color)
		ImU32 textColor;
		if (isPlayer) {
			// Check if player is admin for special text coloring
			bool isAdmin = false;
			auto ident = ent->getPlayerIdentity(scoreboard);
			if (ident && ident->SteamID && ident->SteamID->value && renderBridge) {
				isAdmin = renderBridge->isPlayerAdmin(std::string(ident->SteamID->value));
			}

			// Use green text for admin players, normal player text color for regular players
			if (isAdmin) {
				textColor = IM_COL32(0, 255, 0, 255); // Green text for admin players
			} else {
				textColor = renderBridge->getPlayerTextColor(); // Use player text color setting
			}

			// SEPARATE PLAYER NAME AND DISTANCE POSITIONING
			if (isPlayer) {
				auto ident = ent->getPlayerIdentity(scoreboard);

				// Get individual colors for each text element
				auto nameColor = renderBridge->getPlayerNameColor();
				auto distanceColor = renderBridge->getPlayerDistanceColor();
				auto handColor = renderBridge->getPlayerHandColor();

				// Draw player name above bounding box (CS2-style centered) - with improved overlap prevention
				if (ident && ident->PlayerName && showPlayerName) {
					std::string nameText = ident->PlayerName->value;

					// CS2-STYLE CENTERED POSITIONING: Center the name over the bounding box
					DMARender::Vector2 baseNamePos;
					baseNamePos.x = topW2S.x + (width * 0.5f); // Center horizontally over the bounding box
					baseNamePos.y = topW2S.y - 15.0f; // Above the bounding box

					// Use improved overlap prevention with larger line height for player names
					DMARender::Vector2 finalNamePos = getAvailableTextPosition(baseNamePos, 25.0f);

					// Use CS2-style centered text rendering with stroke and proper size scaling
					drawCenteredTextWithSize(nameText, finalNamePos, playerTextSize, nameColor, true);
				}

				// Draw hand item above player name (CS2-style centered) - Using cached approach with improved overlap prevention
				if (showPlayerHand) {
					// Use cached hand item reading for better performance
					std::string handItemName = ent->getHandItemCached(memUpdater->getVMM(), memUpdater->getPid(), currentFrame);

					if (!handItemName.empty()) {
						// CS2-STYLE CENTERED POSITIONING: Center the hand item above the player name
						DMARender::Vector2 baseHandPos;
						baseHandPos.x = topW2S.x + (width * 0.5f); // Center horizontally over the bounding box
						baseHandPos.y = topW2S.y - 25.0f; // Closer to the player name (reduced from 35.0f to 25.0f)

						// Use improved overlap prevention with medium line height for hand items
						DMARender::Vector2 finalHandPos = getAvailableTextPosition(baseHandPos, 22.0f);

						// Use CS2-style centered text rendering with stroke (slightly smaller than player name)
						float handTextSize = playerTextSize * 0.9f; // 90% of player name size
						drawCenteredTextWithSize(handItemName, finalHandPos, handTextSize, handColor, true);
					}
					// No text displayed when no hand item - clean display
				}

				// Draw player inventory contents (if enabled and entity has cargo)
				// For players, use playerCargoGrid (0x150 offset)
				auto playerCargoGrid = ent->InventoryPtr->getCargoGrid(true); // true = player
				if (renderBridge->shouldShowPlayerInventoryFUSER() && ent->InventoryPtr && playerCargoGrid) {
					// Force refresh to ensure we get current items
					playerCargoGrid->forceRefresh();
					playerCargoGrid->resolveCargoItems(memUpdater->getVMM(), memUpdater->getPid());

					if (playerCargoGrid->hasItems()) {
						float containerTextSize = playerTextSize * 0.8f; // Smaller text for container items
						float yOffset = topW2S.y - 40.0f; // Start above hand item

						auto cargoItems = playerCargoGrid->getCargoItems();
						int itemsToShow = renderBridge->getContainerContentsMaxItems(); // Use configurable limit

						int validItemsShown = 0;
						std::map<std::string, int> itemCounts; // Track item counts for duplicates

						// First pass: count all items
						for (const auto& cargoItem : cargoItems) {
							if (cargoItem && cargoItem->EntityTypePtr && cargoItem->isValid()) {
								std::string itemName = playerCargoGrid->getItemCleanName(cargoItem, memUpdater->getVMM(), memUpdater->getPid());
								if (!itemName.empty()) {
									itemCounts[itemName]++;
								}
							}
						}

						// Second pass: display items with counts
						for (const auto& itemPair : itemCounts) {
							if (validItemsShown >= itemsToShow) break; // Respect max items limit
							
							std::string itemName = itemPair.first;
							int count = itemPair.second;
							
							std::string containerText = itemName;
							if (count > 1) {
								containerText += " (x" + std::to_string(count) + ")";
							}

							DMARender::Vector2 containerPos;
							containerPos.x = topW2S.x + 5.0f; // Slightly to the right
							containerPos.y = yOffset - (validItemsShown * 12.0f); // Stack vertically

							drawText(containerText, containerPos, containerTextSize,
								renderBridge->getContainerContentsColor()); // Use configurable color

							validItemsShown++;
						}

						// Show item count if more than shown
						int totalUniqueItems = (int)itemCounts.size();
						if (totalUniqueItems > validItemsShown) {
							std::string moreText = "+" + std::to_string(totalUniqueItems - validItemsShown) + " more";
							DMARender::Vector2 morePos;
							morePos.x = topW2S.x + 5.0f;
							morePos.y = yOffset - (validItemsShown * 12.0f);
							drawText(moreText, morePos, containerTextSize * 0.9f,
								IM_COL32(150, 150, 150, 255)); // Gray for "more" text
						}
					}
				}

				// Draw distance below entity player (CS2-style centered) - with improved overlap prevention
				if (showPlayerDistance) {
					char distanceBuffer[32];
					sprintf_s(distanceBuffer, "%.0fm", dist);
					std::string distanceText = distanceBuffer;

					// CS2-STYLE CENTERED POSITIONING: Center the distance below the bounding box
					DMARender::Vector2 baseDistancePos;
					baseDistancePos.x = topW2S.x + (width * 0.5f); // Center horizontally over the bounding box
					baseDistancePos.y = originW2S.y + 8.0f; // Below the actual player entity with more spacing

					// Use improved overlap prevention with smaller line height for distance text
					DMARender::Vector2 finalDistancePos = getAvailableTextPosition(baseDistancePos, 18.0f);

					// Use CS2-style centered text rendering with stroke and proper size scaling
					float distanceTextSize = playerTextSize * 0.85f; // Slightly smaller than player name
					drawCenteredTextWithSize(distanceText, finalDistancePos, distanceTextSize, distanceColor, true);
				}
			}
		}
		else if (ent->isZombie()) {
			textColor = renderBridge->getZombieTextColor();
			// Use DayZ2-style overlap prevention for zombies
			DMARender::Vector2 baseTextPos;
			baseTextPos.x = topW2S.x + (width * 0.5f); // Center horizontally relative to entity width
			baseTextPos.y = topW2S.y - 5.0f; // Slightly above the top of the entity

			DMARender::Vector2 finalTextPos = getAvailableTextPosition(baseTextPos, 20.0f);
			drawTextList(infoText, finalTextPos, playerTextSize, textColor);
		}
		else if (ent->isAnimal()) {
			textColor = renderBridge->getAnimalTextColor();
			// Use DayZ2-style overlap prevention for animals
			DMARender::Vector2 baseTextPos;
			baseTextPos.x = topW2S.x + (width * 0.5f); // Center horizontally relative to entity width
			baseTextPos.y = topW2S.y - 5.0f; // Slightly above the top of the entity

			DMARender::Vector2 finalTextPos = getAvailableTextPosition(baseTextPos, 20.0f);
			drawTextList(infoText, finalTextPos, playerTextSize, textColor);
		}
		else {
			textColor = boxColor; // Fallback to box color for other entities
			// Use DayZ2-style overlap prevention for other entities
			DMARender::Vector2 baseTextPos;
			baseTextPos.x = topW2S.x + (width * 0.5f); // Center horizontally relative to entity width
			baseTextPos.y = topW2S.y - 5.0f; // Slightly above the top of the entity

			DMARender::Vector2 finalTextPos = getAvailableTextPosition(baseTextPos, 20.0f);
			drawTextList(infoText, finalTextPos, playerTextSize, textColor);
		}

		ImGui::PopFont();

	}

	// Phase 3: Multithreaded Skeleton Processing
	// Process all collected skeleton entities in parallel
	if (!skeletonProcessingEntities.empty()) {
		printf("[OVERLAY DEBUG] Processing %zu entities with multithreaded skeleton system\n", skeletonProcessingEntities.size());
		
		auto vmm = memUpdater->getVMM();
		auto pid = memUpdater->getPid();
		float thickness = renderBridge->getSkeletonLineThickness();
		int detailLevel = renderBridge->getSkeletonDetailLevel();
		
		// Process skeletons multithreaded
		SkeletonESP::ProcessSkeletonsMultithreaded(
			vmm, pid, camera, skeletonProcessingEntities,
			showPlayerSkeleton, showZombieSkeleton,
			renderBridge->getPlayerSkeletonMaxDistance(), renderBridge->getZombieSkeletonMaxDistance(),
			renderBridge->getPlayerSkeletonColor(), renderBridge->getZombieSkeletonColor(),
			thickness, detailLevel
		);
		
		// Get results and render them
		auto workerSystem = SkeletonESP::GetWorkerSystem();
		if (workerSystem) {
			auto results = workerSystem->GetResults();
			SkeletonESP::RenderSkeletonResults(results);
			printf("[OVERLAY DEBUG] Rendered %zu skeleton results\n", results.size());
		}
		
		// Phase 4: Head Circle Drawing (DayZ mod.txt style)
		// Draw head circles for all entities that have skeletons enabled
		if (renderBridge->isHeadCircleEnabled()) {
			for (const auto& ent : skeletonProcessingEntities) {
				if (!ent || !ent->isValid()) continue;
				
				bool isPlayer = ent->isPlayer();
				bool isZombie = ent->isZombie();
				
				// Only draw head circles if skeleton is enabled for this entity type
				if ((isPlayer && showPlayerSkeleton) || (isZombie && showZombieSkeleton)) {
					ImU32 headCircleColor = isPlayer ? renderBridge->getPlayerSkeletonColor() : renderBridge->getZombieSkeletonColor();
					float headCircleThickness = renderBridge->getSkeletonLineThickness() * 1.5f; // Slightly thicker than skeleton lines
					float headCircleSize = renderBridge->getHeadCircleSize(); // Get size multiplier from settings
					
					// Draw head circle (DayZ mod.txt style)
					SkeletonESP::DrawHeadCircle(vmm, pid, camera, ent, headCircleColor, headCircleThickness, 0.75f, headCircleSize);
				}
			}
		}
		
		// Clear the processing list for next frame
		skeletonProcessingEntities.clear();
	}
}

void DayZ::OverlayAdapter::drawDebugInformation(DayZ::Camera* camera, const std::vector<std::shared_ptr<DayZ::Entity>>& entities)
{
	// Get actual screen resolution dynamically instead of hardcoded values
	auto io = ImGui::GetIO();
	const float screenWidth = io.DisplaySize.x;
	const float screenHeight = io.DisplaySize.y;

	// Get debug distance from settings
	int LootDebugDistance = renderBridge->shouldLootDebugDistance();
	
	// Performance counter for debug info
	static int processedEntities = 0;
	static int validEntities = 0;
	static int skippedEntities = 0;
	processedEntities = 0;
	validEntities = 0;
	skippedEntities = 0;

	for (auto const item : entities) {
		processedEntities++;
		
		// 1. COMPREHENSIVE SAFETY CHECKS
		if (!item) {
			skippedEntities++;
			continue;
		}
		
		if (!item->EntityTypePtr || !item->FutureVisualStatePtr) {
			skippedEntities++;
			continue;
		}
		
		// Validate entity type properties before accessing
		if (!item->EntityTypePtr->TypeName || !item->EntityTypePtr->ConfigName ||
			!item->EntityTypePtr->CleanName || !item->EntityTypePtr->ModelName) {
			skippedEntities++;
			continue;
		}

		// 2. PERFORMANCE OPTIMIZATIONS
		auto itemPos = item->FutureVisualStatePtr->position;
		
		// Early distance check before expensive WorldToScreen calculation
		float dist = camera->InvertedViewTranslation.Dist(itemPos);
		if (dist > LootDebugDistance || dist < 1.0f) {
			skippedEntities++;
			continue; // Skip too far or too close entities
		}

		// 3. SCREEN POSITION CALCULATION
		auto screenPos = DMARender::Vector2();
		if (!WorldToScreenDayZ(camera, itemPos, screenPos)) {
			skippedEntities++;
			continue; // Skip if not visible on screen
		}

		// 5. ENHANCED DEBUG INFORMATION DISPLAY
		float baseFontSize = ImGui::GetFontSize();
		float espMultiplier = renderBridge->getESPTextSize();

		// Clamp multiplier to extended range for more control
		if (espMultiplier < 0.1f) espMultiplier = 0.1f;
		if (espMultiplier > 3.0f) espMultiplier = 3.0f;

		// Optimize scaling curve for better readability at small sizes
		float adjustedMultiplier = espMultiplier;
		if (espMultiplier < 1.0f) {
			adjustedMultiplier = 0.7f + (espMultiplier * 0.3f);
		}

		// Apply improved distance-based scaling for debug text
		float qualityMultiplier = 1.0f;
		if (dist > 50.0f) {
			if (dist <= 100.0f) {
				// 50-100m: Scale from 100% to 85%
				float t = (dist - 50.0f) / 50.0f;
				qualityMultiplier = 1.0f - (t * 0.15f);
			} else if (dist <= 200.0f) {
				// 100-200m: Scale from 85% to 70%
				float t = (dist - 100.0f) / 100.0f;
				qualityMultiplier = 0.85f - (t * 0.15f);
			} else if (dist <= 400.0f) {
				// 200-400m: Scale from 70% to 55%
				float t = (dist - 200.0f) / 200.0f;
				qualityMultiplier = 0.70f - (t * 0.15f);
			} else {
				// 400m+: Minimum 55% size
				qualityMultiplier = 0.55f;
			}
		}

		// Increase base font size for debug info specifically
		float debugTextSize = (baseFontSize * 1.2f) * adjustedMultiplier * qualityMultiplier; // 20% larger base size with distance scaling

		// Ensure minimum readable size
		if (debugTextSize < 10.0f) debugTextSize = 10.0f; // Minimum 10px for readability (reduced for distance scaling)
		if (debugTextSize > 24.0f) debugTextSize = 24.0f; // Maximum 24px to avoid oversized text
		
		float lineHeight = debugTextSize + 4.0f; // Increased spacing between lines for better readability
		float currentY = screenPos.y + 20; // Start 20 pixels below entity

		// 6. COMPREHENSIVE DEBUG INFORMATION
		// Line 1: Network ID and Entity Type
		std::string entityInfo = "ID: " + std::to_string(item->NetworkID) + " | Type: " + std::string(item->EntityTypePtr->TypeName->value);
		drawText(entityInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, IM_COL32(255, 255, 0, 255)); // Yellow
		currentY += lineHeight;

		// Line 2: Position (X, Y, Z)
		std::string positionInfo = "Pos: (" + std::format("{:.1f}", itemPos.x) + ", " + 
								   std::format("{:.1f}", itemPos.y) + ", " + 
								   std::format("{:.1f}", itemPos.z) + ")";
		drawText(positionInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, IM_COL32(0, 255, 255, 255)); // Cyan
		currentY += lineHeight;

		// Line 3: Distance and Clean Name (with string validation)
		std::string cleanName = "Unknown";
		if (item->EntityTypePtr->CleanName && item->EntityTypePtr->CleanName->value) {
			std::string rawName = std::string(item->EntityTypePtr->CleanName->value);
			// Validate string length and content
			if (rawName.length() > 0 && rawName.length() <= 10 && 
				rawName.find('\0') == std::string::npos && 
				rawName.find("$UNT$") == std::string::npos) {
				cleanName = rawName;
			}
		}
		std::string distanceInfo = "Dist: " + std::format("{:.1f}m", dist) + " | " + cleanName;
		drawText(distanceInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, IM_COL32(0, 255, 0, 255)); // Green
		currentY += lineHeight;

		// Line 4: Config Name (with string validation)
		std::string configName = "Unknown";
		if (item->EntityTypePtr->ConfigName && item->EntityTypePtr->ConfigName->value) {
			std::string rawConfig = std::string(item->EntityTypePtr->ConfigName->value);
			// Validate string length and content
			if (rawConfig.length() > 0 && rawConfig.length() <= 10 && 
				rawConfig.find('\0') == std::string::npos && 
				rawConfig.find("$UNT$") == std::string::npos) {
				configName = rawConfig;
			}
		}
		std::string configInfo = "Config: " + configName;
		drawText(configInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, IM_COL32(255, 128, 0, 255)); // Orange
		currentY += lineHeight;

		// Line 5: Model Name (with string validation)
		std::string modelName = "Unknown";
		if (item->EntityTypePtr->ModelName && item->EntityTypePtr->ModelName->value) {
			std::string rawModel = std::string(item->EntityTypePtr->ModelName->value);
			// Validate string length and content
			if (rawModel.length() > 0 && rawModel.length() <= 10 && 
				rawModel.find('\0') == std::string::npos && 
				rawModel.find("$UNT$") == std::string::npos) {
				modelName = rawModel;
			}
		}
		std::string modelInfo = "Model: " + modelName;
		drawText(modelInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, IM_COL32(128, 0, 255, 255)); // Purple
		currentY += lineHeight;

		// Line 6: Entity Status (Valid, Dead, etc.) with Best String validation
		std::string bestString = "N/A";
		if (item->EntityTypePtr->getBestString() && item->EntityTypePtr->getBestString()->value) {
			std::string rawBest = std::string(item->EntityTypePtr->getBestString()->value);
			// Validate string length and content
			if (rawBest.length() > 0 && rawBest.length() <= 10 && 
				rawBest.find('\0') == std::string::npos && 
				rawBest.find("$UNT$") == std::string::npos) {
				bestString = rawBest;
			}
		}
		std::string statusInfo = "Valid: " + std::string(item->isValid() ? "Yes" : "No") + 
								" | Dead: " + std::string(item->isDead ? "Yes" : "No") +
								" | Best: " + bestString;
		ImU32 statusColor = item->isValid() ? IM_COL32(0, 255, 0, 255) : IM_COL32(255, 0, 0, 255); // Green if valid, Red if invalid
		drawText(statusInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, statusColor);
		currentY += lineHeight;

		// Line 7: Additional Entity Properties (if available) with validation
		if (item->InventoryPtr && item->InventoryPtr->isHandItemValid && 
			item->InventoryPtr->handItem && item->InventoryPtr->handItem->EntityTypePtr) {
			
			std::string handItemName = "Unknown";
			if (item->InventoryPtr->handItem->EntityTypePtr->getBestString() && 
				item->InventoryPtr->handItem->EntityTypePtr->getBestString()->value) {
				
				std::string rawHand = std::string(item->InventoryPtr->handItem->EntityTypePtr->getBestString()->value);
				// Validate string length and content
				if (rawHand.length() > 0 && rawHand.length() <= 10 && 
					rawHand.find('\0') == std::string::npos && 
					rawHand.find("$UNT$") == std::string::npos) {
					handItemName = rawHand;
				}
			}
			std::string handInfo = "Hand Item: " + handItemName;
			drawText(handInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, IM_COL32(255, 255, 255, 255)); // White
			currentY += lineHeight;
		}

		// Line 8: Memory Address Information (for advanced debugging)
		std::string addressInfo = "Addr: 0x" + std::format("{:X}", item->_remoteAddress);
		drawText(addressInfo, DMARender::Vector2(screenPos.x, currentY), debugTextSize, IM_COL32(128, 128, 128, 255)); // Gray
		currentY += lineHeight;

		validEntities++;
	}

	// 7. PERFORMANCE STATISTICS (displayed in top-left corner)
	if (renderBridge->getEnableESPDebugLogging()) {
		float baseFontSize = ImGui::GetFontSize();
		float statsTextSize = baseFontSize * 1.1f; // 10% larger than base for better readability
		
		// Ensure minimum readable size for stats
		if (statsTextSize < 16.0f) statsTextSize = 16.0f; // Minimum 16px for stats readability
		
		std::string statsInfo = "Debug Stats: Processed=" + std::to_string(processedEntities) + 
							   " | Valid=" + std::to_string(validEntities) + 
							   " | Skipped=" + std::to_string(skippedEntities) +
							   " | Screen=" + std::format("{:.0f}x{:.0f}", screenWidth, screenHeight);
		drawText(statsInfo, DMARender::Vector2(10, 10), statsTextSize, IM_COL32(255, 255, 255, 230)); // More opaque white for better visibility
	}
}










// Helper function to log suspicious entities
void logSuspiciousEntity(std::shared_ptr<DayZ::Entity> entity, const std::string& reason, bool loggingEnabled) {
	if (!loggingEnabled) return;

	static std::ofstream logFile("esp_debug.log", std::ios::app);
	static bool headerWritten = false;
	static std::unordered_set<std::string> loggedEntityNames;
	static std::chrono::steady_clock::time_point lastClearTime = std::chrono::steady_clock::now();

	// Clear the logged entities list every 30 seconds to allow new entities to be logged
	auto now = std::chrono::steady_clock::now();
	if (now - lastClearTime > std::chrono::seconds(30)) {
		loggedEntityNames.clear();
		lastClearTime = now;
	}

	if (!headerWritten) {
		logFile << "=== ESP Debug Log ===" << std::endl;
		headerWritten = true;
	}

	auto pos = entity->FutureVisualStatePtr->position;
	std::string entityName = "Unknown";
	if (entity->EntityTypePtr && entity->EntityTypePtr->getBestString()) {
		entityName = entity->EntityTypePtr->getBestString()->value;
	}

	// Create a unique identifier for this entity type and reason
	std::string logKey = entityName + "_" + reason;
	
	// Skip if we've already logged this entity type with this reason recently
	if (loggedEntityNames.find(logKey) != loggedEntityNames.end()) {
		return; // Skip duplicate logging
	}
	
	// Mark this entity type as logged
	loggedEntityNames.insert(logKey);

	logFile << "[" << reason << "] NetworkID: " << entity->NetworkID
			<< " | Name: " << entityName
			<< " | Pos: (" << pos.x << ", " << pos.y << ", " << pos.z << ")"
			<< " | Valid: " << (entity->isValid() ? "Yes" : "No")
			<< " | Dead: " << (entity->isDead ? "Yes" : "No")
			<< " | Time: " << std::format("{:%H:%M:%S}", std::chrono::system_clock::now())
			<< std::endl;
	logFile.flush();
}

// Enhanced entity logging for invisible object filtering
void logAllEntities(std::shared_ptr<DayZ::Entity> entity, const std::string& category) {
	static std::ofstream allEntitiesLog;
	static bool logInitialized = false;
	static std::unordered_set<std::string> loggedEntityNames;
	static std::chrono::steady_clock::time_point lastClearTime = std::chrono::steady_clock::now();

	// Clear the logged entities list every 60 seconds to allow new entities to be logged
	auto now = std::chrono::steady_clock::now();
	if (now - lastClearTime > std::chrono::seconds(60)) {
		loggedEntityNames.clear();
		lastClearTime = now;
	}

	// Initialize log file with full path and error checking
	if (!logInitialized) {
		try {
			allEntitiesLog.open("C:\\temp\\all_entities.log", std::ios::app);
			if (allEntitiesLog.is_open()) {
				allEntitiesLog << "=== All Entities Log for Filtering ===" << std::endl;
				allEntitiesLog << "Category,NetworkID,Name,PosX,PosY,PosZ,Valid,Dead,Distance,Timestamp" << std::endl;
				allEntitiesLog.flush();
				logInitialized = true;
			} else {
				// Fallback to current directory
				allEntitiesLog.open("all_entities.log", std::ios::app);
				if (allEntitiesLog.is_open()) {
					allEntitiesLog << "=== All Entities Log for Filtering ===" << std::endl;
					allEntitiesLog << "Category,NetworkID,Name,PosX,PosY,PosZ,Valid,Dead,Distance,Timestamp" << std::endl;
					allEntitiesLog.flush();
					logInitialized = true;
				}
			}
		} catch (...) {
			// Silent fail - logging is optional
			return;
		}
	}

	// Only log if file is properly opened
	if (!allEntitiesLog.is_open()) {
		return;
	}

	try {
		auto pos = entity->FutureVisualStatePtr->position;
		std::string entityName = "Unknown";
		if (entity->EntityTypePtr && entity->EntityTypePtr->getBestString()) {
			entityName = entity->EntityTypePtr->getBestString()->value;
		}

		// Create a unique identifier for this entity type and category
		std::string logKey = entityName + "_" + category;
		
		// Skip if we've already logged this entity type in this category recently
		if (loggedEntityNames.find(logKey) != loggedEntityNames.end()) {
			return; // Skip duplicate logging
		}
		
		// Mark this entity type as logged
		loggedEntityNames.insert(logKey);

		// Calculate distance from origin for reference
		float distance = sqrt(pos.x * pos.x + pos.y * pos.y + pos.z * pos.z);

		// Get current timestamp
		auto timestamp = std::chrono::system_clock::now();
		auto time_t = std::chrono::system_clock::to_time_t(timestamp);
		std::string timeStr = std::format("{:%H:%M:%S}", timestamp);

		allEntitiesLog << category << "," << entity->NetworkID
					   << "," << entityName
					   << "," << pos.x << "," << pos.y << "," << pos.z
					   << "," << (entity->isValid() ? "1" : "0")
					   << "," << (entity->isDead ? "1" : "0")
					   << "," << distance
					   << "," << timeStr
					   << std::endl;
		allEntitiesLog.flush();
	} catch (...) {
		// Silent fail - don't crash ESP if logging fails
	}
}

// Legacy caching functions - replaced by selective caching system
bool DayZ::OverlayAdapter::isEntityCached(uint32_t entityId, float currentDistance) {
	// This function is deprecated - use selective caching instead
	return false;
}

void DayZ::OverlayAdapter::updateEntityCache(uint32_t entityId, float distance, bool isVisible) {
	// This function is deprecated - use selective caching instead
	// For position caching, use cachePosition() instead
}

void DayZ::OverlayAdapter::clearExpiredCache() {
	// This function is deprecated - use selective caching instead
	clearExpiredNameCache();
	clearExpiredPositionCache();
}

void DayZ::OverlayAdapter::batchProcessEntities(const std::vector<std::shared_ptr<DayZ::Entity>>& entities) {
	batchEntities.clear();
	batchEntities.reserve(entities.size());
	
	for (const auto& entity : entities) {
		if (entity && entity->isValid()) {
			batchEntities.push_back(entity);
		}
	}
}

bool DayZ::OverlayAdapter::shouldRenderEntity(const DMARender::Vector3& position, const DMARender::Vector3& cameraPos, float maxDistance) {
	float distance = cameraPos.Dist(position);
	return distance >= MIN_RENDER_DISTANCE && distance <= maxDistance;
}

// Phase 5: Type-based rendering optimization
// Categorizes entities by type and prepares them for efficient batch rendering
std::vector<DayZ::OverlayAdapter::TypedEntity> DayZ::OverlayAdapter::categorizeEntities(
	const std::vector<std::shared_ptr<DayZ::Entity>>& entities, DayZ::Camera* camera, bool isLootMode)
{
	std::vector<TypedEntity> typedEntities;
	typedEntities.reserve(entities.size());
	
	for (const auto& entity : entities) {
		if (!entity || !entity->EntityTypePtr || !entity->FutureVisualStatePtr) {
			continue;
		}
		
		TypedEntity typedEntity;
		typedEntity.entity = entity;
		typedEntity.type = determineEntityType(entity);
		
		// Calculate distance
		auto entityPos = entity->FutureVisualStatePtr->position;
		typedEntity.distance = camera->InvertedViewTranslation.Dist(entityPos);
		
		// Calculate screen position
		if (!WorldToScreenDayZ(camera, entityPos, typedEntity.screenPos)) {
			continue; // Skip if not visible on screen
		}
		typedEntity.isVisible = true;
		
		// Get color based on entity type
		typedEntity.color = getEntityColor(entity, typedEntity.type);
		
		// Build display text based on entity type and mode
		if (isLootMode) {
			// Loot mode - show CleanName only (high performance approach)
			if (entity->EntityTypePtr->CleanName &&
				entity->EntityTypePtr->CleanName->length > 0 &&
				entity->EntityTypePtr->CleanName->length < 400 &&
				entity->EntityTypePtr->CleanName->value) {
				typedEntity.displayText = entity->EntityTypePtr->CleanName->value;

				// Use custom name for rare items if available
				auto lootManager = renderBridge->getLootListManager();
				if (lootManager && entity->EntityTypePtr->TypeName) {
					std::string itemName = entity->EntityTypePtr->TypeName->value;
					if (lootManager->isRareItem(itemName)) {
						typedEntity.displayText = lootManager->getDisplayName(itemName);
					}
				}
			}
			
			// Add distance
			typedEntity.displayText += " " + std::to_string(static_cast<int>(typedEntity.distance)) + "m";
			
			// Add postfix for dead entities
			if (entity->isDead) {
				typedEntity.displayText += " (Dead)";
			}
		}
		else {
			// Alive entities mode - show name/type and distance
			if (typedEntity.type == EntityRenderType::PLAYER) {
				// Player name handling would go here
				typedEntity.displayText = "Player";
			}
			else if (typedEntity.type == EntityRenderType::ZOMBIE) {
				typedEntity.displayText = "Zombie";
			}
			else if (typedEntity.type == EntityRenderType::ANIMAL) {
				if (entity->EntityTypePtr->getBestString()) {
					typedEntity.displayText = entity->EntityTypePtr->getBestString()->value;
				}
				else {
					typedEntity.displayText = "Animal";
				}
			}
			
			// Add distance
			typedEntity.displayText += " " + std::to_string(static_cast<int>(typedEntity.distance)) + "m";
		}
		
		typedEntities.push_back(typedEntity);
	}
	
	// Sort by type first, then by distance within each type for efficient rendering
	std::sort(typedEntities.begin(), typedEntities.end(), 
		[](const TypedEntity& a, const TypedEntity& b) {
			if (a.type != b.type) {
				return static_cast<int>(a.type) < static_cast<int>(b.type);
			}
			return a.distance < b.distance;
		});
	
	return typedEntities;
}

// Determines the render type of an entity
DayZ::OverlayAdapter::EntityRenderType DayZ::OverlayAdapter::determineEntityType(const std::shared_ptr<DayZ::Entity>& entity)
{
	if (!entity || !entity->EntityTypePtr) {
		return EntityRenderType::OTHER;
	}
	
	// Check for alive entities first
	if (entity->isPlayer()) {
		return entity->isDead ? EntityRenderType::DEAD_PLAYER : EntityRenderType::PLAYER;
	}
	if (entity->isZombie()) {
		return EntityRenderType::ZOMBIE;
	}
	if (entity->isAnimal()) {
		return entity->isDead ? EntityRenderType::DEAD_ANIMAL : EntityRenderType::ANIMAL;
	}
	
	// Check for loot items
	if (entity->isCar()) {
		return EntityRenderType::VEHICLE;
	}
	if (entity->isBoat()) {
		return EntityRenderType::BOAT;
	}
	if (entity->isRare()) {
		return EntityRenderType::RARE_ITEM;
	}
	if (entity->isWeapon()) {
		return EntityRenderType::WEAPON;
	}
	if (entity->isClothing()) {
		return EntityRenderType::CLOTHING;
	}
	if (entity->isBackpack()) {
		return EntityRenderType::BACKPACK;
	}
	if (entity->isFood()) {
		return EntityRenderType::FOOD;
	}
	if (entity->isAmmo()) {
		return EntityRenderType::AMMO;
	}
	if (entity->isProxyMagazines()) {
		return EntityRenderType::PROXY_MAGAZINE;
	}
	if (entity->isOptic()) {
		return EntityRenderType::OPTIC;
	}
	if (entity->isBase()) {
		return EntityRenderType::BASE_BUILDING;
	}
	if (entity->isMelee()) {
		return EntityRenderType::MELEE;
	}
	if (entity->isExplosives()) {
		return EntityRenderType::EXPLOSIVE;
	}
	if (entity->isGroundItem()) {
		return EntityRenderType::GROUND_ITEM;
	}
	if (entity->isContainer()) {
		return EntityRenderType::CONTAINER;
	}
	if (entity->isCooking()) {
		return EntityRenderType::COOKING;
	}
	if (entity->isCamping()) {
		return EntityRenderType::CAMPING;
	}
	if (entity->isStash()) {
		return EntityRenderType::STASH;
	}
	
	return EntityRenderType::OTHER;
}

// Gets the appropriate color for an entity based on its type
ImU32 DayZ::OverlayAdapter::getEntityColor(const std::shared_ptr<DayZ::Entity>& entity, EntityRenderType type)
{
	switch (type) {
		case EntityRenderType::PLAYER:
			// Check if player is admin for special coloring
			if (entity && renderBridge) {
				auto ident = entity->getPlayerIdentity(memUpdater->getScoreboard().get());
				if (ident && ident->SteamID && ident->SteamID->value) {
					if (renderBridge->isPlayerAdmin(std::string(ident->SteamID->value))) {
						return IM_COL32(0, 255, 0, 255); // Green for admin players
					}
				}
			}
			return renderBridge->getPlayerTextColor();
		case EntityRenderType::ZOMBIE:
			return renderBridge->getZombieTextColor();
		case EntityRenderType::ANIMAL:
			return renderBridge->getAnimalTextColor();
		case EntityRenderType::DEAD_PLAYER:
			return renderBridge->getDeadPlayerTextColor();
		case EntityRenderType::DEAD_ANIMAL:
			return renderBridge->getAnimalTextColor(); // Use same as living animals
		case EntityRenderType::VEHICLE:
		case EntityRenderType::BOAT:
			return renderBridge->getVehicleTextColor();
		case EntityRenderType::RARE_ITEM:
			// Get custom color from LootListManager
			if (renderBridge && entity->EntityTypePtr->TypeName) {
				auto lootManager = renderBridge->getLootListManager();
				if (lootManager) {
					std::string itemName = entity->EntityTypePtr->TypeName->value;
					return lootManager->getItemColor(itemName);
				}
			}
			return IM_COL32(255, 0, 255, 255); // Fallback purple
		case EntityRenderType::WEAPON:
			return renderBridge->getWeaponColor();
		case EntityRenderType::CLOTHING:
			return renderBridge->getClothingColor();
		case EntityRenderType::BACKPACK:
			return renderBridge->getBackpackColor();
		case EntityRenderType::FOOD:
			return renderBridge->getFoodColor();
		case EntityRenderType::AMMO:
			return renderBridge->getAmmoColor();
		case EntityRenderType::PROXY_MAGAZINE:
			return renderBridge->getProxyMagazineColor();
		case EntityRenderType::OPTIC:
			return renderBridge->getOpticColor();
		case EntityRenderType::BASE_BUILDING:
			return renderBridge->getBaseBuildingColor();
		case EntityRenderType::MELEE:
			return renderBridge->getMeleeColor();
		case EntityRenderType::EXPLOSIVE:
			return renderBridge->getExplosiveColor();
		case EntityRenderType::CONTAINER:
			return renderBridge->getContainerColor();
		case EntityRenderType::COOKING:
			return renderBridge->getCookingColor();
		case EntityRenderType::CAMPING:
			return renderBridge->getCampingColor();
		case EntityRenderType::STASH:
			return renderBridge->getStashColor();
		case EntityRenderType::GROUND_ITEM:
			return renderBridge->getGroundItemColor();
		default:
			return IM_COL32(255, 255, 255, 255); // White
	}
}

// Renders entities grouped by type for efficient batch rendering
void DayZ::OverlayAdapter::renderEntitiesByType(const std::vector<TypedEntity>& typedEntities)
{
	if (typedEntities.empty()) return;
	
	// Group entities by type and render in batches
	EntityRenderType currentType = EntityRenderType::OTHER;
	ImFont* currentFont = nullptr;
	
	for (const auto& typedEntity : typedEntities) {
		// Check if we need to switch rendering context
		if (typedEntity.type != currentType) {
			// Switch font if needed (pop previous font first)
			if (currentFont) {
				ImGui::PopFont();
			}
			
			// Select appropriate font for this entity type
			if (typedEntity.type == EntityRenderType::PLAYER ||
				typedEntity.type == EntityRenderType::ZOMBIE ||
				typedEntity.type == EntityRenderType::ANIMAL ||
				typedEntity.type == EntityRenderType::DEAD_PLAYER ||
				typedEntity.type == EntityRenderType::DEAD_ANIMAL) {
				currentFont = playerFont;
			}
			else {
				currentFont = lootFont;
			}
			
			// Use fallback font if selected font is not available
			if (!currentFont || !currentFont->IsLoaded()) {
				currentFont = ImGui::GetIO().Fonts->Fonts[0];
			}
			
			ImGui::PushFont(currentFont);
			currentType = typedEntity.type;
		}
		
		// Calculate text size based on entity type and distance - consistent with player text
		float baseFontSize = currentFont->FontSize;
		float espMultiplier = renderBridge->getESPTextSize();
		
		// Clamp multiplier
		if (espMultiplier < 0.1f) espMultiplier = 0.1f;
		if (espMultiplier > 3.0f) espMultiplier = 3.0f;
		
		// Optimize scaling curve
		float adjustedMultiplier = espMultiplier;
		if (espMultiplier < 1.0f) {
			adjustedMultiplier = 0.7f + (espMultiplier * 0.3f);
		}
		
		// Apply improved distance-based scaling for better readability
		float qualityMultiplier = 1.0f;

		// Smooth distance-based scaling that starts early for better visual clarity
		if (typedEntity.distance > 50.0f) {
			// Start scaling at 50m for better visual clarity
			if (typedEntity.distance <= 100.0f) {
				// 50-100m: Scale from 100% to 85%
				float t = (typedEntity.distance - 50.0f) / 50.0f; // 0.0 to 1.0
				qualityMultiplier = 1.0f - (t * 0.15f); // 1.0 to 0.85
			} else if (typedEntity.distance <= 200.0f) {
				// 100-200m: Scale from 85% to 70%
				float t = (typedEntity.distance - 100.0f) / 100.0f; // 0.0 to 1.0
				qualityMultiplier = 0.85f - (t * 0.15f); // 0.85 to 0.70
			} else if (typedEntity.distance <= 400.0f) {
				// 200-400m: Scale from 70% to 55%
				float t = (typedEntity.distance - 200.0f) / 200.0f; // 0.0 to 1.0
				qualityMultiplier = 0.70f - (t * 0.15f); // 0.70 to 0.55
			} else {
				// 400m+: Minimum 55% size
				qualityMultiplier = 0.55f;
			}
		}
		
		float textSize = baseFontSize * adjustedMultiplier * qualityMultiplier;
		
		// Render the entity with DayZ2-style enhanced font system
		FontType fontType = (typedEntity.distance > 100.0f) ?
			FontType::LOOT_FAR :
			FontType::LOOT_NEAR;
		drawEnhancedTextWithSize(typedEntity.displayText, typedEntity.screenPos, textSize, typedEntity.color, true);

		// Draw container contents for loot items (backpacks, clothing, containers, etc.)
		// For items/containers, use regular cargoGrid (0x148 offset)
		auto itemCargoGrid = typedEntity.entity->InventoryPtr ? typedEntity.entity->InventoryPtr->getCargoGrid(false) : nullptr; // false = item
		if (renderBridge->shouldShowContainerContents() && typedEntity.entity &&
			(typedEntity.entity->isBackpack() || typedEntity.entity->isClothing() ||
			 typedEntity.entity->isContainer() || typedEntity.entity->isCooking() ||
			 typedEntity.entity->isCamping() || typedEntity.entity->isStash()) &&
			typedEntity.entity->InventoryPtr && itemCargoGrid &&
			typedEntity.distance <= renderBridge->getContainerContentsMaxDistance()) {

			// Force refresh to ensure we get current items
			itemCargoGrid->forceRefresh();
			itemCargoGrid->resolveCargoItems(memUpdater->getVMM(), memUpdater->getPid());

			if (itemCargoGrid->hasItems()) {
				float containerTextSize = textSize * 0.7f; // Smaller text for container items
				auto cargoItems = itemCargoGrid->getCargoItems();
				int maxItems = renderBridge->getContainerContentsMaxItems();
				int itemsToShow = ((int)cargoItems.size() < maxItems) ? (int)cargoItems.size() : maxItems; // Use configurable limit

				int validItemsShown = 0;
				std::map<std::string, int> itemCounts; // Track item counts for duplicates

				// First pass: count all items
				for (const auto& cargoItem : cargoItems) {
					if (cargoItem && cargoItem->EntityTypePtr && cargoItem->isValid()) {
						std::string itemName = itemCargoGrid->getItemCleanName(cargoItem, memUpdater->getVMM(), memUpdater->getPid());
						if (!itemName.empty()) {
							itemCounts[itemName]++;
						}
					}
				}

				// Second pass: display items with counts
				for (const auto& itemPair : itemCounts) {
					if (validItemsShown >= maxItems) break; // Respect max items limit
					
					std::string itemName = itemPair.first;
					int count = itemPair.second;
					
					std::string containerText = itemName;
					if (count > 1) {
						containerText += " (x" + std::to_string(count) + ")";
					}

					DMARender::Vector2 containerPos;
					containerPos.x = typedEntity.screenPos.x + 10.0f; // Slightly to the right
					containerPos.y = typedEntity.screenPos.y + 15.0f + (validItemsShown * 12.0f); // Below main text, stack vertically

					// Use DayZ2-style enhanced font rendering for container contents
					drawEnhancedTextWithSize(containerText, containerPos, containerTextSize,
						renderBridge->getContainerContentsColor(), true); // Use configurable color with stroke

					validItemsShown++;
				}

				// Show item count if more than shown
				int totalUniqueItems = (int)itemCounts.size();
				if (totalUniqueItems > validItemsShown) {
					std::string moreText = "+" + std::to_string(totalUniqueItems - validItemsShown) + " more";
					DMARender::Vector2 morePos;
					morePos.x = typedEntity.screenPos.x + 10.0f;
					morePos.y = typedEntity.screenPos.y + 15.0f + (validItemsShown * 12.0f);
					// Use DayZ2-style enhanced font rendering for "more items" text
					drawEnhancedTextWithSize(moreText, morePos, containerTextSize * 0.9f,
						IM_COL32(120, 120, 120, 200), true); // Gray with transparency and stroke
				}
			}
		}
	}
	
	// Pop the last font
	if (currentFont) {
		ImGui::PopFont();
	}
}

// Helper function to extract positions from entities for batching

// Selective caching implementation - cache names but not positions
bool DayZ::OverlayAdapter::isPlayerNameCached(uint32_t entityId) {
	auto it = playerNameCache.find(entityId);
	if (it == playerNameCache.end()) {
		return false;
	}
	
	auto now = std::chrono::steady_clock::now();
	auto age = now - it->second.timestamp;
	return age < NAME_CACHE_DURATION && it->second.isValid;
}

std::string DayZ::OverlayAdapter::getCachedPlayerName(uint32_t entityId) {
	auto it = playerNameCache.find(entityId);
	if (it != playerNameCache.end()) {
		return it->second.playerName;
	}
	return "";
}

void DayZ::OverlayAdapter::cachePlayerName(uint32_t entityId, const std::string& playerName, const std::string& steamId) {
	PlayerNameCacheEntry entry;
	entry.timestamp = std::chrono::steady_clock::now();
	entry.playerName = playerName;
	entry.steamId = steamId;
	entry.isValid = true;
	
	playerNameCache[entityId] = entry;
}

void DayZ::OverlayAdapter::clearExpiredNameCache() {
	auto now = std::chrono::steady_clock::now();
	
	// Remove expired entries
	for (auto it = playerNameCache.begin(); it != playerNameCache.end();) {
		auto age = now - it->second.timestamp;
		if (age > NAME_CACHE_DURATION) {
			it = playerNameCache.erase(it);
		} else {
			++it;
		}
	}
}

void DayZ::OverlayAdapter::clearAllNameCache() {
	// Manually clear all player name cache entries
	playerNameCache.clear();
}

// Position caching (temporary, frame-based only)
bool DayZ::OverlayAdapter::isPositionCached(uint32_t entityId) {
	auto it = positionCache.find(entityId);
	if (it == positionCache.end()) {
		return false;
	}
	
	auto now = std::chrono::steady_clock::now();
	auto age = now - it->second.timestamp;
	return age < POSITION_CACHE_DURATION;
}

DayZ::PositionCacheEntry DayZ::OverlayAdapter::getCachedPosition(uint32_t entityId) {
	auto it = positionCache.find(entityId);
	if (it != positionCache.end()) {
		return it->second;
	}
	
	// Return empty entry if not found
	PositionCacheEntry emptyEntry;
	emptyEntry.position = {0, 0, 0};
	emptyEntry.screenPos = {0, 0};
	emptyEntry.distance = 0;
	emptyEntry.isVisible = false;
	emptyEntry.timestamp = std::chrono::steady_clock::now();
	return emptyEntry;
}

bool DayZ::OverlayAdapter::shouldShowCrosshairBasedOnInput() const {
	std::cout << "[CrosshairDebug] shouldShowCrosshairBasedOnInput() called!" << std::endl;

	if (!memUpdater) {
		std::cout << "[CrosshairDebug] No memory updater - showing crosshair" << std::endl;
		return true; // Default to showing crosshair if no memory updater
	}

	DayZ::Mem* mem = memUpdater->getMem();
	if (!mem) {
		std::cout << "[CrosshairDebug] No mem instance - showing crosshair" << std::endl;
		return true; // Default to showing crosshair if no mem instance
	}

	std::cout << "[CrosshairDebug] About to update input state..." << std::endl;

	// Update input state (this is safe to call frequently due to internal rate limiting)
	mem->updateInputState();

	// Check if right mouse button is pressed on target PC
	bool rightMousePressed = mem->isRightMousePressed();

	std::cout << "[CrosshairDebug] Right mouse state: " << (rightMousePressed ? "PRESSED" : "RELEASED")
			  << " - Crosshair will be " << (rightMousePressed ? "SHOWN" : "HIDDEN") << std::endl;

	return rightMousePressed;
}

void DayZ::OverlayAdapter::cachePosition(uint32_t entityId, const DMARender::Vector3& position, const DMARender::Vector2& screenPos, float distance, bool isVisible) {
	PositionCacheEntry entry;
	entry.position = position;
	entry.screenPos = screenPos;
	entry.distance = distance;
	entry.isVisible = isVisible;
	entry.timestamp = std::chrono::steady_clock::now();
	
	positionCache[entityId] = entry;
}

void DayZ::OverlayAdapter::clearExpiredPositionCache() {
	auto now = std::chrono::steady_clock::now();
	
	// Remove expired entries (positions expire quickly)
	for (auto it = positionCache.begin(); it != positionCache.end();) {
		auto age = now - it->second.timestamp;
		if (age > POSITION_CACHE_DURATION) {
			it = positionCache.erase(it);
		} else {
			++it;
		}
	}
}

// Zombie name caching implementation
bool DayZ::OverlayAdapter::isZombieNameCached(uint32_t entityId) {
	auto it = zombieNameCache.find(entityId);
	if (it == zombieNameCache.end()) {
		return false;
	}
	
	auto now = std::chrono::steady_clock::now();
	auto age = now - it->second.timestamp;
	return age < ENTITY_NAME_CACHE_DURATION && it->second.isValid;
}

std::string DayZ::OverlayAdapter::getCachedZombieName(uint32_t entityId) {
	auto it = zombieNameCache.find(entityId);
	if (it != zombieNameCache.end()) {
		return it->second.entityName;
	}
	return "";
}

void DayZ::OverlayAdapter::cacheZombieName(uint32_t entityId, const std::string& zombieName) {
	EntityNameCacheEntry entry;
	entry.timestamp = std::chrono::steady_clock::now();
	entry.entityName = zombieName;
	entry.isValid = true;
	
	zombieNameCache[entityId] = entry;
}

void DayZ::OverlayAdapter::clearExpiredZombieNameCache() {
	auto now = std::chrono::steady_clock::now();
	
	// Remove expired entries
	for (auto it = zombieNameCache.begin(); it != zombieNameCache.end();) {
		auto age = now - it->second.timestamp;
		if (age > ENTITY_NAME_CACHE_DURATION) {
			it = zombieNameCache.erase(it);
		} else {
			++it;
		}
	}
}

void DayZ::OverlayAdapter::clearAllZombieNameCache() {
	// Manually clear all zombie name cache entries
	zombieNameCache.clear();
}

// Animal name caching implementation
bool DayZ::OverlayAdapter::isAnimalNameCached(uint32_t entityId) {
	auto it = animalNameCache.find(entityId);
	if (it == animalNameCache.end()) {
		return false;
	}
	
	auto now = std::chrono::steady_clock::now();
	auto age = now - it->second.timestamp;
	return age < ENTITY_NAME_CACHE_DURATION && it->second.isValid;
}

std::string DayZ::OverlayAdapter::getCachedAnimalName(uint32_t entityId) {
	auto it = animalNameCache.find(entityId);
	if (it != animalNameCache.end()) {
		return it->second.entityName;
	}
	return "";
}

void DayZ::OverlayAdapter::cacheAnimalName(uint32_t entityId, const std::string& animalName) {
	auto now = std::chrono::steady_clock::now();
	animalNameCache[entityId] = { now, animalName, true };
}

void DayZ::OverlayAdapter::clearExpiredAnimalNameCache() {
	auto now = std::chrono::steady_clock::now();
	for (auto it = animalNameCache.begin(); it != animalNameCache.end();) {
		if (now - it->second.timestamp > ENTITY_NAME_CACHE_DURATION) {
			it = animalNameCache.erase(it);
		} else {
			++it;
		}
	}
}

void DayZ::OverlayAdapter::clearAllAnimalNameCache() {
	animalNameCache.clear();
}


