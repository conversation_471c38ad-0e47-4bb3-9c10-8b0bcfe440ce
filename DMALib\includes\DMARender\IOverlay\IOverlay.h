#pragma once
#include <d3dtypes.h>
#include "../Vectors/Vector3.h"
#include "../Vectors/Vector2.h"
#include "imgui.h"
#include <string>
#include <vector>
#include <memory>

// Forward declaration to avoid circular dependency
namespace DMARender {
	class RenderBridge;
}

namespace DMARender {
	struct IOverlay
	{
	protected:
		// Bridge pointer for accessing font style settings
		std::shared_ptr<RenderBridge> renderBridge;

		bool WorldToScreen(const _D3DMATRIX& view_matrix, const DMARender::Vector3& position, DMARender::Vector2& outScreenPos, const float& renderWidth, const float& renderHeight);
		void drawBoundingBox(const Vector2& top, const Vector2& bottom, float width, const ImU32& color);
		void drawFilledCircle(const Vector2& point, const float& radius, const ImU32& color);
		void drawText(const std::string& text, const Vector2& point, const float& size, const ImU32& color);
		void drawTextList(const std::vector<std::string>& strings, const Vector2& pos, const float& size, const ImU32& color);
		void drawCrosshair();

		// Virtual method for input-based crosshair visibility that derived classes can override
		virtual bool shouldShowCrosshairBasedOnInput() const { return true; } // Default: always show

		// Helper function to apply alpha transparency to color
		ImU32 applyAlphaToColor(const ImU32& color, float alpha) const;
	public:
		// Set bridge for accessing font style settings
		void setBridge(std::shared_ptr<RenderBridge> bridge) { renderBridge = bridge; }

		// Public method for aim point calculation
	

		virtual void DrawOverlay() = 0;
		virtual void createFonts() {};
	};
}


