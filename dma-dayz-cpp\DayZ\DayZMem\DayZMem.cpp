#include "DayZMem.h"
#include <algorithm>
#include <future>
#include <thread>

// Check for C++17 parallel algorithms support
#if __cplusplus >= 201703L && defined(__cpp_lib_execution)
    #include <execution>
    #define HAS_PARALLEL_ALGORITHMS 1
#else
    #define HAS_PARALLEL_ALGORITHMS 0
#endif

DMAMem::VmmManager* DayZ::Mem::getVMM()
{
	return vmmManager;
}

DayZ::Mem::Mem(DMAMem::VmmManager* vmmManager)
{
	this->staticManager = DMAMem::StaticManager(vmmManager);
	this->vmmManager = vmmManager;
	fetchBaseAddresses();

	// Initialize input reader
	this->inputReader = std::make_unique<InputReader>(vmmManager, getPid());
	if (this->inputReader) {
		// Allocate console for debug output
		AllocConsole();
		freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
		freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);

		this->inputReader->initialize();
		this->inputReader->setDebugMode(true); // Enable debug mode for troubleshooting

		std::cout << "[DEBUG] InputReader initialized successfully!" << std::endl;
	}
}

void DayZ::Mem::fetchBaseAddresses() {
	auto pid = getPid();
	auto baseModule = staticManager.getModule(pid, EXE_NAME);

	this->worldAddress = baseModule.pvmEntry->vaBase + 0xF4B0A0;
	std::cout << " [ + ] World Address: 0x" << std::hex << this->worldAddress << std::endl;
	this->networkManagerAddress = baseModule.pvmEntry->vaBase + 0xF5E1E0;
	std::cout << " [ + ] Network Manager Address: 0x" << std::hex << this->networkManagerAddress << std::endl;

};

DWORD DayZ::Mem::getPid() {
	return staticManager.getPid(EXE_NAME);
}

DayZ::WorldPointer DayZ::Mem::getWorld()
{
	DayZ::WorldPointer wp;
	wp.resolveObject(getVMM(), getPid(), this->worldAddress);
	return wp;
}

DayZ::NetworkManager DayZ::Mem::getNetworkManager()
{
	DayZ::NetworkManager nm;
	nm.resolveObject(getVMM(), getPid(), this->networkManagerAddress);
	return nm;
}

std::vector<std::shared_ptr<DayZ::Entity>> DayZ::Mem::getAllUniqueEntities()
{
	auto world = this->getWorld().WorldPtr;
	auto uniqueEntities = std::map<std::string, std::shared_ptr<DayZ::Entity>>();
	std::mutex mapMutex; // Mutex for thread-safe map access
	
	// Helper function to generate a unique key for an entity
	auto getEntityKey = [](const std::shared_ptr<DayZ::Entity>& ent) -> std::string {
		if (!ent->isValid() || !ent->FutureVisualStatePtr) return "";
		
		auto pos = ent->FutureVisualStatePtr->position;
		// Round positions to 2 decimal places to handle floating point imprecision
		float x = round(pos.x * 100) / 100;
		float y = round(pos.y * 100) / 100;
		float z = round(pos.z * 100) / 100;
		
		return std::to_string(ent->NetworkID) + "_" + 
			   std::to_string(x) + "_" + 
			   std::to_string(y) + "_" + 
			   std::to_string(z);
	};
	
	// Multi-core optimized entity processing
	auto processTableAsync = [&](const std::vector<std::shared_ptr<DayZ::Entity>>& entities) -> std::future<void> {
		return std::async(std::launch::async, [&, entities]() {
			std::map<std::string, std::shared_ptr<DayZ::Entity>> localEntities;

			// Use parallel algorithms for large entity sets on many-core systems
			#if HAS_PARALLEL_ALGORITHMS
			if (entities.size() > 100 && std::thread::hardware_concurrency() >= 8) {
				// Parallel processing for large entity sets
				std::mutex localMutex;
				std::for_each(std::execution::par_unseq, entities.begin(), entities.end(),
					[&](const std::shared_ptr<DayZ::Entity>& entity) {
						if (entity && entity->isValid()) {
							

							std::lock_guard<std::mutex> lock(localMutex);
							std::string key = getEntityKey(entity);
							if (!key.empty()) {
								localEntities[key] = entity;
							}
						}
					});
			} else {
			#endif
			// Sequential processing for smaller sets or limited cores
			for (const auto& ent : entities) {
				if (!ent->isValid()) continue;

				

				std::string key = getEntityKey(ent);
				if (key.empty()) continue;

				// Only add if we haven't seen this exact entity before
				if (localEntities.find(key) == localEntities.end()) {
					localEntities[key] = ent;
				}
			}
			#if HAS_PARALLEL_ALGORITHMS
			}
			#endif

		// Merge local results with global map
		std::lock_guard<std::mutex> lock(mapMutex);
		for (const auto& pair : localEntities) {
			if (uniqueEntities.find(pair.first) == uniqueEntities.end()) {
				uniqueEntities[pair.first] = pair.second;
			}
		}
	});
	};
	
	// Process all tables in parallel
	std::vector<std::future<void>> futures;
	futures.push_back(processTableAsync(world->NearEntityTable->resolvedEntities));
	futures.push_back(processTableAsync(world->FarEntityTable->resolvedEntities));
	futures.push_back(processTableAsync(world->SlowEntityTable->resolvedEntities));
	futures.push_back(processTableAsync(world->ItemTable->resolvedEntities));
	
	// Wait for all processing to complete
	for (auto& future : futures) {
		future.wait();
	}
	
	// Convert map to vector
	std::vector<std::shared_ptr<DayZ::Entity>> returnSet;
	returnSet.reserve(uniqueEntities.size());
	for (const auto& pair : uniqueEntities) {
		returnSet.push_back(pair.second);
	}
	
	return returnSet;
}

bool DayZ::Mem::isRightMousePressed() const {
	if (!inputReader) {
		return false;
	}
	return inputReader->isRightMousePressed();
}

void DayZ::Mem::updateInputState() {
	if (inputReader) {
		inputReader->update();
	}
}
