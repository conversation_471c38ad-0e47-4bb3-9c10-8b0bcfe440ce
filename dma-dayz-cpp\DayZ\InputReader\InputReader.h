#pragma once
#include "DMAMemoryManagement/includes.h"
#include <Windows.h>

namespace DayZ {
    class InputReader {
    private:
        DMAMem::VmmManager* vmmManager = nullptr;
        DWORD targetPid = 0;
        
        // Windows input state memory locations
        // These are common locations where Windows stores input state
        static constexpr QWORD USER32_MOUSE_STATE_OFFSET = 0x0; // Will be dynamically found
        static constexpr QWORD WIN32K_INPUT_STATE_OFFSET = 0x0; // Will be dynamically found
        
        // Mouse button state cache
        bool rightMousePressed = false;
        bool leftMousePressed = false;
        bool middleMousePressed = false;
        
        // Performance optimization
        DWORD lastUpdateTick = 0;
        static constexpr DWORD UPDATE_INTERVAL_MS = 16; // ~60Hz update rate
        
        // Memory addresses for input state
        QWORD inputStateAddress = 0;
        bool addressResolved = false;
        
        // Helper methods
        bool resolveInputStateAddress();
        bool readMouseButtonState();
        bool readFromUser32InputState();
        bool readFromWin32kInputState();
        bool readFromRawInputBuffer();
        bool readFromAlternativeMethod();
        
    public:
        InputReader(DMAMem::VmmManager* vmm, DWORD pid);
        ~InputReader();
        
        // Main interface methods
        bool initialize();
        void update();
        
        // Mouse state getters
        bool isRightMousePressed() const { return rightMousePressed; }
        bool isLeftMousePressed() const { return leftMousePressed; }
        bool isMiddleMousePressed() const { return middleMousePressed; }
        
        // Utility methods
        bool isInitialized() const { return addressResolved; }
        void forceUpdate(); // Force immediate update regardless of timing
        
        // Debug methods
        QWORD getInputStateAddress() const { return inputStateAddress; }
        void setDebugMode(bool enabled) { debugMode = enabled; }
        
    private:
        bool debugMode = false;
        void debugLog(const std::string& message);
    };
}
