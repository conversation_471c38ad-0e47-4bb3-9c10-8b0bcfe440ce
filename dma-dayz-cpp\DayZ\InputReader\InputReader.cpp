#include "InputReader.h"
#include <iostream>
#include <chrono>

namespace DayZ {

InputReader::InputReader(DMAMem::VmmManager* vmm, DWORD pid) 
    : vmmManager(vmm), targetPid(pid) {
    debugLog("InputReader initialized for PID: " + std::to_string(pid));
}

InputReader::~InputReader() {
    debugLog("InputReader destroyed");
}

bool InputReader::initialize() {
    if (!vmmManager) {
        debugLog("ERROR: VmmManager is null");
        return false;
    }
    
    if (targetPid == 0) {
        debugLog("ERROR: Target PID is 0");
        return false;
    }
    
    // Try to resolve input state address
    if (!resolveInputStateAddress()) {
        debugLog("WARNING: Could not resolve input state address, will try alternative methods");
        // Don't return false here - we can still try other methods
    }
    
    debugLog("InputReader initialization completed");
    return true;
}

void InputReader::update() {
    DWORD currentTick = GetTickCount();
    
    // Rate limiting for performance
    if (currentTick - lastUpdateTick < UPDATE_INTERVAL_MS) {
        return;
    }
    
    lastUpdateTick = currentTick;
    
    // Try to read mouse button state using various methods
    if (!readMouseButtonState()) {
        debugLog("WARNING: Failed to read mouse button state");
    }
}

void InputReader::forceUpdate() {
    lastUpdateTick = 0; // Reset timing
    update();
}

bool InputReader::resolveInputStateAddress() {
    // Method 1: Try to find User32.dll and locate input state
    if (readFromUser32InputState()) {
        addressResolved = true;
        debugLog("Successfully resolved input state via User32");
        return true;
    }
    
    // Method 2: Try Win32k.sys input state
    if (readFromWin32kInputState()) {
        addressResolved = true;
        debugLog("Successfully resolved input state via Win32k");
        return true;
    }
    
    // Method 3: Try raw input buffer
    if (readFromRawInputBuffer()) {
        addressResolved = true;
        debugLog("Successfully resolved input state via Raw Input");
        return true;
    }
    
    debugLog("Failed to resolve input state address");
    return false;
}

bool InputReader::readMouseButtonState() {
    // Try multiple methods to read mouse state

    if (debugMode) {
        debugLog("Reading mouse button state...");
    }

    // Method 1: Read from resolved address if available
    if (addressResolved && inputStateAddress != 0) {
        try {
            // Read a small structure that typically contains mouse button states
            struct MouseState {
                BYTE leftButton;
                BYTE rightButton;
                BYTE middleButton;
                BYTE reserved;
            } mouseState = {0};

            if (vmmManager->readMemory(targetPid, inputStateAddress, &mouseState, sizeof(mouseState))) {
                leftMousePressed = (mouseState.leftButton & 0x80) != 0;
                rightMousePressed = (mouseState.rightButton & 0x80) != 0;
                middleMousePressed = (mouseState.middleButton & 0x80) != 0;

                if (debugMode) {
                    debugLog("Mouse state via resolved address - L:" + std::to_string(leftMousePressed) +
                            " R:" + std::to_string(rightMousePressed) +
                            " M:" + std::to_string(middleMousePressed));
                }
                return true;
            }
        } catch (...) {
            debugLog("Exception reading from resolved address");
        }
    }

    // Method 2: Try reading from User32 input state
    if (debugMode) {
        debugLog("Trying User32 input state method...");
    }
    if (readFromUser32InputState()) {
        return true;
    }

    // Method 3: Try reading from Win32k input state
    if (debugMode) {
        debugLog("Trying Win32k input state method...");
    }
    if (readFromWin32kInputState()) {
        return true;
    }

    if (debugMode) {
        debugLog("All DMA methods failed, mouse state detection unsuccessful");
    }
    return false;
}

bool InputReader::readFromUser32InputState() {
    // This method attempts to read actual mouse button state from the target PC
    // We'll use a combination of approaches to detect real input state

    try {
        // Method 1: Try to read from Windows' GetAsyncKeyState equivalent in target process
        // We need to find where Windows stores the async key state in the target process

        // Get User32.dll base address in target process
        PVMMDLL_MAP_MODULEENTRY pModuleEntry;
        if (!VMMDLL_Map_GetModuleFromNameU(vmmManager->getVmm(), targetPid,
                                          (LPSTR)"user32.dll", &pModuleEntry, 0)) {
            debugLog("Failed to get User32.dll module");
            return readFromAlternativeMethod();
        }

        debugLog("Found User32.dll at: 0x" + std::to_string(pModuleEntry->vaBase));

        // Method 2: Try to read from Windows message queue or input state
        // Look for the global input state structure in User32.dll
        QWORD searchStart = pModuleEntry->vaBase + 0x1000;
        QWORD searchEnd = pModuleEntry->vaBase + pModuleEntry->cbImageSize;

        // Search for input state patterns in User32.dll data sections
        for (QWORD addr = searchStart; addr < searchEnd; addr += 0x1000) {
            BYTE buffer[256] = {0};
            if (vmmManager->readMemory(targetPid, addr, buffer, sizeof(buffer))) {
                // Look for patterns that might indicate mouse button state
                // Windows typically stores input state as bit flags
                for (int i = 0; i < 240; i += 4) {
                    DWORD* dwordPtr = (DWORD*)(buffer + i);

                    // Check if this looks like an input state structure
                    // Right mouse button is typically VK_RBUTTON (0x02)
                    if ((*dwordPtr & 0x02) && (*dwordPtr & 0x8000)) {
                        rightMousePressed = true;
                        debugLog("Detected right mouse pressed via pattern search");
                        return true;
                    }
                }
            }
        }

        // If pattern search fails, try alternative method
        return readFromAlternativeMethod();

    } catch (...) {
        debugLog("Exception in readFromUser32InputState");
        return readFromAlternativeMethod();
    }
}

bool InputReader::readFromWin32kInputState() {
    // This method would read from Win32k.sys input state
    // This is more complex as it requires kernel memory access
    
    debugLog("Win32k input state reading not implemented yet");
    return false;
}

bool InputReader::readFromRawInputBuffer() {
    // This method would read from the raw input buffer
    // This is another alternative approach

    debugLog("Raw input buffer reading not implemented yet");
    return false;
}

bool InputReader::readFromAlternativeMethod() {
    // Simplified approach for testing: Just use GetAsyncKeyState directly
    // This will detect right mouse button regardless of window focus for now

    try {
        debugLog("Using simplified GetAsyncKeyState method");

        // Check right mouse button state directly
        SHORT keyState = GetAsyncKeyState(VK_RBUTTON);
        bool wasPressed = rightMousePressed;
        rightMousePressed = (keyState & 0x8000) != 0;

        if (debugMode) {
            if (rightMousePressed != wasPressed) {
                debugLog("Right mouse state changed: " + std::string(rightMousePressed ? "PRESSED" : "RELEASED"));
            }
        }

        return true;

    } catch (...) {
        debugLog("Exception in readFromAlternativeMethod");
        rightMousePressed = false;
        return false;
    }
}

void InputReader::debugLog(const std::string& message) {
    if (debugMode) {
        std::cout << "[InputReader] " << message << std::endl;
    }
}

} // namespace DayZ
